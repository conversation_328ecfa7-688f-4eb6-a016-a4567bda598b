"use server";

import {
    type ConditionDependency,
    type ConditionQuestion,
    type ConditionType,
    type ConditionValue,
    type DateRangeConditionValue,
    type RangeConditionValue
} from "@/components/forms/fields/condition-selector";
import { Database, Json } from "@/types/database.types";
import { createClientFromRequest } from "@/utils/supabase/server";

interface ActionResult<T> {
    success: boolean;
    data?: T;
    error?: string;
}

export async function getScholarshipDocumentTypes(scholarshipId: string): Promise<ActionResult<string[]>> {
    try {
        const supabase = await createClientFromRequest();

        const { data, error } = await supabase
            .from("link_scholarship_to_document_type")
            .select("document_type_id")
            .eq("scholarship_id", scholarshipId);

        if (error) {
            throw error;
        }

        return {
            success: true,
            data: data.map((item) => item.document_type_id)
        };
    } catch (error) {
        console.error("Error fetching scholarship document types:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Failed to fetch scholarship document types"
        };
    }
}

export async function updateScholarshipDocumentTypes(
    scholarshipId: string,
    documentTypeIds: string[]
): Promise<ActionResult<void>> {
    try {
        const supabase = await createClientFromRequest();

        const { error: deleteError } = await supabase
            .from("link_scholarship_to_document_type")
            .delete()
            .eq("scholarship_id", scholarshipId);

        if (deleteError) {
            throw deleteError;
        }

        if (documentTypeIds.length > 0) {
            const connections = documentTypeIds.map((documentTypeId) => ({
                scholarship_id: scholarshipId,
                document_type_id: documentTypeId
            }));

            const { error: insertError } = await supabase.from("link_scholarship_to_document_type").insert(connections);

            if (insertError) {
                throw insertError;
            }
        }

        return { success: true };
    } catch (error) {
        console.error("Error updating scholarship document types:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Failed to update scholarship document types"
        };
    }
}

export async function getScholarshipConditionGroupsForScholarship(
    scholarshipId: string
): Promise<ActionResult<string[]>> {
    try {
        const supabase = await createClientFromRequest();

        const { data, error } = await supabase
            .from("link_scholarship_to_condition_groups")
            .select("group_id")
            .eq("scholarship_id", scholarshipId);

        if (error) {
            throw error;
        }

        return {
            success: true,
            data: data.map((item) => item.group_id)
        };
    } catch (error) {
        console.error("Error fetching scholarship condition groups:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Failed to fetch scholarship condition groups"
        };
    }
}

export async function updateScholarshipConditionGroups(
    scholarshipId: string,
    conditionGroupIds: string[]
): Promise<ActionResult<void>> {
    try {
        const supabase = await createClientFromRequest();

        const { error: deleteError } = await supabase
            .from("link_scholarship_to_condition_groups")
            .delete()
            .eq("scholarship_id", scholarshipId);

        if (deleteError) {
            throw deleteError;
        }

        if (conditionGroupIds.length > 0) {
            const connections = conditionGroupIds.map((groupId) => ({
                scholarship_id: scholarshipId,
                group_id: groupId
            }));

            const { error: insertError } = await supabase
                .from("link_scholarship_to_condition_groups")
                .insert(connections);

            if (insertError) {
                throw insertError;
            }
        }

        return { success: true };
    } catch (error) {
        console.error("Error updating scholarship condition groups:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Failed to update scholarship condition groups"
        };
    }
}

export async function getAllQuestionsForConditionSelector(): Promise<ActionResult<ConditionQuestion[]>> {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase.from("questions").select(`
                id,
                type,
                metadata,
                groups_question ( id, name )
            `);

        if (error) throw error;

        interface QuestionDataFromDb {
            id: string;
            type: Database["public"]["Enums"]["question_type"];
            metadata: {
                label?: string;
                placeholder?: string;
                options?: string | Array<{ value: string; label: string }>;
            } | null;
            groups_question: { id: string; name: string } | { id: string; name: string }[] | null;
        }

        const formattedData = (data as QuestionDataFromDb[]).map((q: QuestionDataFromDb): ConditionQuestion => {
            let questionGroup = undefined;
            if (q.groups_question) {
                if (Array.isArray(q.groups_question)) {
                    if (q.groups_question.length > 0) {
                        questionGroup = q.groups_question[0];
                    }
                } else {
                    questionGroup = q.groups_question;
                }
            }

            return {
                id: q.id,
                type: q.type,
                metadata: {
                    label: (q.metadata as { label?: string })?.label || undefined,
                    placeholder: (q.metadata as { placeholder?: string })?.placeholder || undefined,
                    options: (q.metadata as { options?: string | Array<{ value: string; label: string }> })?.options || undefined
                },
                groups_question: questionGroup
            };
        });

        return { success: true, data: formattedData };
    } catch (error) {
        console.error("Error fetching questions for condition selector:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Failed to fetch questions"
        };
    }
}

export async function getScholarshipDefinedConditions(
    scholarshipId: string
): Promise<ActionResult<ConditionDependency[]>> {
    try {
        const supabase = await createClientFromRequest();

        const { data: links, error: linkError } = await supabase
            .from("link_scholarship_to_condition")
            .select("condition_id")
            .eq("scholarship_id", scholarshipId);

        if (linkError) throw linkError;

        if (!links || links.length === 0) {
            return { success: true, data: [] };
        }

        const conditionIds = links.map((link) => link.condition_id);

        const { data: conditions, error: conditionsError } = await supabase
            .from("conditions")
            .select("id, question_id, type, value")
            .in("id", conditionIds);

        if (conditionsError) throw conditionsError;

        if (!conditions || conditions.length === 0) {
            return { success: true, data: [] };
        }

        const questionIds = conditions.map((c) => c.question_id);
        const { data: questions, error: questionsError } = await supabase
            .from("questions")
            .select("id, type")
            .in("id", questionIds);

        if (questionsError) throw questionsError;

        const questionTypeMap = new Map<string, Database["public"]["Enums"]["question_type"]>();

        type QuestionRecord = {
            id: string;
            type: Database["public"]["Enums"]["question_type"];
        };

        (questions as QuestionRecord[])?.forEach((q) => {
            if (q.id && q.type) {
                questionTypeMap.set(q.id, q.type);
            }
        });

        const formattedData = conditions.map((condition) => {
            const questionType = questionTypeMap.get(condition.question_id);

            let correctConditionType: ConditionType = condition.type as ConditionType;
            if (questionType) {
                if (questionType === "number_input") {
                    correctConditionType = "range";
                } else if (questionType === "date_picker") {
                    correctConditionType = "date_range";
                } else if (["single_select", "multi_select"].includes(questionType)) {
                    correctConditionType = "in";
                }
            }

            let formattedValue: ConditionValue;
            switch (correctConditionType) {
                case "range":
                    if (
                        condition.value &&
                        typeof condition.value === "object" &&
                        !Array.isArray(condition.value) &&
                        condition.value !== null
                    ) {
                        formattedValue = condition.value as RangeConditionValue;
                    } else {
                        formattedValue = { min: undefined, max: undefined };
                    }
                    break;
                case "date_range":
                    if (
                        condition.value &&
                        typeof condition.value === "object" &&
                        !Array.isArray(condition.value) &&
                        condition.value !== null
                    ) {
                        formattedValue = condition.value as unknown as DateRangeConditionValue;
                    } else {
                        formattedValue = { operator: "greater_than", days_from_today: 0 };
                    }
                    break;
                case "in":
                default:
                    formattedValue = Array.isArray(condition.value)
                        ? (condition.value as string[]).map((val) => ({ id: val, label: val }))
                        : [];
                    break;
            }

            return {
                question_id: condition.question_id,
                condition_type: correctConditionType,
                condition_value: formattedValue
            } as unknown as ConditionDependency;
        });

        return { success: true, data: formattedData };
    } catch (error) {
        console.error("Error fetching defined scholarship conditions:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Failed to fetch defined conditions"
        };
    }
}

export async function updateScholarshipDefinedConditions(
    scholarshipId: string,
    conditions: ConditionDependency[]
): Promise<ActionResult<void>> {
    try {
        const supabase = await createClientFromRequest();

        const { data: existingLinks, error: fetchError } = await supabase
            .from("link_scholarship_to_condition")
            .select("condition_id")
            .eq("scholarship_id", scholarshipId);

        if (fetchError) throw fetchError;

        const existingConditionIds = existingLinks.map((link) => link.condition_id);

        if (existingConditionIds.length > 0) {
            const { error: deleteLinkError } = await supabase
                .from("link_scholarship_to_condition")
                .delete()
                .eq("scholarship_id", scholarshipId);
            if (deleteLinkError) throw deleteLinkError;
        }

        if (existingConditionIds.length > 0) {
            const { data: stillUsed, error: checkError } = await supabase
                .from("link_scholarship_to_condition")
                .select("condition_id")
                .neq("scholarship_id", scholarshipId)
                .in("condition_id", existingConditionIds);

            if (checkError) {
                console.error("Error checking condition references:", checkError);
                throw checkError;
            }

            const stillUsedIds = stillUsed.map((item) => item.condition_id);
            const safeToDeleteIds = existingConditionIds.filter((id) => !stillUsedIds.includes(id));

            if (safeToDeleteIds.length > 0) {
                const { error: deleteConditionError } = await supabase
                    .from("conditions")
                    .delete()
                    .in("id", safeToDeleteIds);

                if (deleteConditionError) {
                    console.error("Error deleting existing conditions:", deleteConditionError);
                    throw deleteConditionError;
                }
            }
        }

        if (conditions.length > 0) {
            const conditionsToInsert = conditions.map((cond) => {
                const questionId = typeof cond.question_id === 'object' ? cond.question_id.id : cond.question_id;
                let condValue = cond.condition_value;

                if (
                    cond.condition_type === "range" &&
                    (!condValue || typeof condValue !== "object" || Array.isArray(condValue) || condValue === null)
                ) {
                    condValue = { min: undefined, max: undefined };
                } else if (
                    cond.condition_type === "date_range" &&
                    (!condValue || typeof condValue !== "object" || Array.isArray(condValue) || condValue === null)
                ) {
                    condValue = { operator: "greater_than", days_from_today: 0 };
                } else if (cond.condition_type === "in" && !Array.isArray(condValue)) {
                    condValue = [];
                }

                return {
                    question_id: questionId,
                    type: cond.condition_type as "range" | "date_range" | "in",
                    value: condValue as Json
                };
            });

            const { data: insertedConditions, error: insertConditionError } = await supabase
                .from("conditions")
                .insert(conditionsToInsert)
                .select("id");

            if (insertConditionError) throw insertConditionError;
            if (!insertedConditions) throw new Error("Failed to insert conditions or retrieve IDs");

            const linksToInsert = insertedConditions.map((cond) => ({
                scholarship_id: scholarshipId,
                condition_id: cond.id
            }));

            const { error: insertLinkError } = await supabase
                .from("link_scholarship_to_condition")
                .insert(linksToInsert);

            if (insertLinkError) throw insertLinkError;
        }

        return { success: true };
    } catch (error) {
        console.error("Error updating defined scholarship conditions:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Failed to update defined conditions"
        };
    }
}
