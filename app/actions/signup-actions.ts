"use server";

import { auth } from "@clerk/nextjs/server";

import { TEXTS } from "@/lib/auth-constants";
import { getSupabaseClient } from "@/utils/supabase/client";
import { setUserClaim, UserClaim<PERSON>ey } from "@/utils/user-claims-client";

export interface SignupPreferencesData {
    acceptedTerms: boolean;
    subscribeNewsletter: boolean;
}

export async function saveSignupPreferences(
    data: SignupPreferencesData
): Promise<{ success: boolean; error?: string }> {
    try {
        const { userId } = await auth();

        if (!userId) {
            return { success: false, error: TEXTS.unauthorizedError };
        }

        if (typeof data.acceptedTerms !== "boolean" || typeof data.subscribeNewsletter !== "boolean") {
            return { success: false, error: TEXTS.invalidDataError };
        }

        const supabase = getSupabaseClient();

        await Promise.all([
            setUserClaim(supabase, userId, UserClaimKey.ACCEPTED_TERMS, data.acceptedTerms),
            setUserClaim(supabase, userId, UserClaimKey.SUBSCRIBED_TO_UPDATES, data.subscribeNewsletter)
        ]);

        return { success: true };
    } catch (error) {
        console.error("Error saving signup preferences:", error);
        return { success: false, error: TEXTS.savePreferencesError };
    }
}
