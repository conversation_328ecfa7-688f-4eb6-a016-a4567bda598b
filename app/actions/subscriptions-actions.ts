"use server";

import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";

import { FEATURES, PRICING_PLANS } from "@/config/subscriptions";
import subscriptions from "@/config/subscriptions";
import {
    type CouponResult,
    Feature,
    type PaymentDetails,
    type PaymentParams,
    SubscriptionsItem,
    TEXTS,
    UserSubscriptionWithPlan,
    type ValidatedCoupon,
    type VerificationResult
} from "@/lib/subscription-constants";
import { extractCCode, getPaymentErrorMessage } from "@/utils/payment-errors";
import { createClientFromRequest } from "@/utils/supabase/server";

// Helper functions (moved from lib/subscriptions.ts)
function getAppliedDiscountAmount(
    originalPrice: number,
    coupon: { couponType: "fixed_amount" | "percentage"; discountValue: number }
): number {
    if (originalPrice <= 0) {
        return 0;
    }
    let discount = 0;
    if (coupon.couponType === "percentage") {
        discount = (originalPrice * coupon.discountValue) / 100;
    } else if (coupon.couponType === "fixed_amount") {
        discount = coupon.discountValue;
    } else {
        console.warn(`Unknown coupon type: ${coupon.couponType}. Applying no discount.`);
        return 0;
    }
    return Math.round(Math.min(originalPrice, discount) * 100) / 100;
}

function extractPlanIdFromOrderId(orderId: string): string {
    const parts = orderId.split("_");
    return parts[0];
}

async function parseQueryString(queryString: string): Promise<Record<string, string>> {
    const params: Record<string, string> = {};
    const searchParams = new URLSearchParams(queryString);
    searchParams.forEach((value, key) => {
        params[key] = value;
    });
    return params;
}

export async function getPlans(): Promise<SubscriptionsItem[]> {
    return PRICING_PLANS;
}

export async function getFeatures(): Promise<Record<string, Feature>> {
    return FEATURES;
}

export async function getCurrentUserSubscription(): Promise<UserSubscriptionWithPlan | null> {
    const supabase = await createClientFromRequest();

    const { userId } = await auth();

    if (!userId) {
        return null;
    }

    const { data, error } = await supabase
        .from("user_subscriptions")
        .select("*")
        .eq("user_id", userId)
        .eq("is_active", true)
        .or(`expiration_date.gte.${new Date().toISOString()},expiration_date.is.null`)
        .order("created_at", { ascending: false })
        .limit(1);

    if (error) {
        console.error("Error fetching user subscription:", error);
        return null;
    }

    if (!data || data.length === 0) {
        return null;
    }

    const subscription = data[0];
    const planDetails = subscriptions.plans.find((plan) => plan.id === subscription.plan_id);

    return {
        ...subscription,
        planType: planDetails?.planType || "free"
    } as UserSubscriptionWithPlan;
}

export async function calculateDiscount(originalPrice: number, coupon: ValidatedCoupon | null): Promise<number> {
    if (!coupon) {
        return 0;
    }
    return getAppliedDiscountAmount(originalPrice, {
        couponType: coupon.coupon_type,
        discountValue: coupon.discount_value
    });
}

export async function calculateFinalPrice(
    originalPrice: number,
    coupon: { couponType: "fixed_amount" | "percentage"; discountValue: number } | null
): Promise<number> {
    if (!coupon || originalPrice <= 0) {
        return Math.round(originalPrice * 100) / 100;
    }

    const discountAmount = getAppliedDiscountAmount(originalPrice, coupon);
    const finalPrice = originalPrice - discountAmount;

    return Math.round(Math.max(0, finalPrice) * 100) / 100;
}

export async function extractCouponFromOrderId(gatewayOrderId: string): Promise<string | null> {
    const couponPrefix = "_COUPON_";
    const index = gatewayOrderId.indexOf(couponPrefix);
    if (index !== -1) {
        return gatewayOrderId.substring(index + couponPrefix.length);
    }
    return null;
}

export async function validateCoupon(
    coupon_code: string
): Promise<{ isValid: true; coupon: ValidatedCoupon } | { isValid: false; error: string }> {
    const supabase = await createClientFromRequest();
    const now = new Date().toISOString();

    const { data: coupon, error: fetchError } = await supabase
        .from("coupons")
        .select("*, coupon_type")
        .eq("coupon_code", coupon_code)
        .single();

    if (fetchError || !coupon) {
        return { isValid: false, error: TEXTS.COUPON_NOT_FOUND };
    }

    if (coupon.expiration_date && coupon.expiration_date < now) {
        return { isValid: false, error: TEXTS.COUPON_EXPIRED };
    }

    if (coupon.usage_limit !== null && coupon.used_count >= coupon.usage_limit) {
        return { isValid: false, error: TEXTS.COUPON_USAGE_LIMIT_REACHED };
    }

    return { isValid: true, coupon: coupon as ValidatedCoupon };
}

export async function validateAndApplyCoupon(coupon_code: string, totalAmount: number): Promise<CouponResult> {
    const validationResult = await validateCoupon(coupon_code);

    if (!validationResult.isValid) {
        return { success: false, error: validationResult.error, finalAmount: totalAmount };
    }

    const { coupon } = validationResult;

    const discountApplied = getAppliedDiscountAmount(totalAmount, {
        couponType: coupon.coupon_type,
        discountValue: coupon.discount_value
    });

    const finalAmount = Math.round(Math.max(0, totalAmount - discountApplied) * 100) / 100;

    return {
        success: true,
        couponCode: coupon.coupon_code,
        couponType: coupon.coupon_type,
        discountValue: coupon.discount_value,
        discountApplied,
        finalAmount
    };
}

export async function incrementCouponUsage(coupon_code: string): Promise<{ success: boolean; error?: string }> {
    const supabase = await createClientFromRequest();

    const { data, error: updateError } = await supabase.rpc("increment_coupon_usage", { p_coupon_code: coupon_code });

    if (updateError) {
        return { success: false, error: TEXTS.COUPON_UPDATE_ERROR };
    }

    if (!data || data.length === 0) {
        return { success: false, error: TEXTS.COUPON_NOT_FOUND };
    }

    if (!data[0].incremented) {
        return { success: false, error: TEXTS.COUPON_USAGE_LIMIT_REACHED };
    }

    return { success: true };
}

export async function applyCoupon(coupon_code: string, totalAmount: number): Promise<CouponResult> {
    const result = await validateAndApplyCoupon(coupon_code, totalAmount);

    if (!result.success) {
        return result;
    }

    const incrementResult = await incrementCouponUsage(coupon_code);
    if (!incrementResult.success) {
        return {
            success: false,
            error: incrementResult.error,
            finalAmount: totalAmount
        };
    }

    return result;
}

export async function updateUserSubscription(
    userId: string,
    planId: string,
    paymentDetails?: PaymentDetails,
    couponCode?: string
): Promise<{ success: boolean; error?: string }> {
    const supabase = await createClientFromRequest();

    try {
        if (paymentDetails?.transactionId) {
            const { data: existingTransaction, error: transactionError } = await supabase
                .from("user_subscriptions")
                .select("id, transaction_id, start_date, expiration_date")
                .eq("transaction_id", paymentDetails.transactionId)
                .single();

            if (!transactionError && existingTransaction) {
                return { success: true };
            }
        }

        const plan = subscriptions.plans.find((p) => p.id === planId);

        if (!plan) {
            console.error("Error finding plan:", planId);
            return { success: false, error: `Plan not found: ${planId}` };
        }

        const now = new Date();
        let expirationDate: Date | null = null;
        if (plan.duration_days) {
            expirationDate = new Date(now);
            expirationDate.setDate(expirationDate.getDate() + (plan.duration_days || 0));
        }

        const subscriptionData = {
            user_id: userId,
            plan_id: planId,
            start_date: now.toISOString(),
            expiration_date: expirationDate ? expirationDate.toISOString() : null,
            is_active: true,
            plan_price: plan.price,
            paid_amount: plan.price,
            payment_details: paymentDetails || null,
            transaction_id: paymentDetails?.transactionId,
            order_id: paymentDetails?.orderId
        };

        const { data, error } = await supabase.rpc("update_user_subscription_with_coupon", {
            p_user_id: userId,
            p_plan_id: planId,
            p_subscription_data: subscriptionData,
            p_coupon_code: couponCode || null
        });

        if (error) {
            console.error("Error in atomic subscription update:", error);
            return { success: false, error: "Failed to update subscription" };
        }

        if (!data || data.length === 0) {
            return { success: false, error: "No response from subscription update" };
        }

        const result = data[0];
        if (!result.success) {
            return { success: false, error: result.error_message || "Failed to update subscription" };
        }

        revalidatePath("/dashboard");
        revalidatePath("/account");

        return { success: true };
    } catch (error) {
        console.error("Unexpected error in updateUserSubscription:", error);
        return { success: false, error: "An unexpected error occurred" };
    }
}

export async function verifyPaymentAndUpdateSubscription(paymentParams: PaymentParams): Promise<VerificationResult> {
    const PAYMENT_GATEWAY_MERCHANT_ID = process.env.PAYMENT_GATEWAY_MERCHANT_ID;
    const PAYMENT_GATEWAY_API_KEY = process.env.PAYMENT_GATEWAY_API_KEY;
    const PAYMENT_GATEWAY_PASSP = process.env.PAYMENT_GATEWAY_PASSP;
    const PAYMENT_GATEWAY_VERIFY_URL = process.env.PAYMENT_GATEWAY_VERIFY_URL;

    if (
        !PAYMENT_GATEWAY_MERCHANT_ID ||
        !PAYMENT_GATEWAY_API_KEY ||
        !PAYMENT_GATEWAY_PASSP ||
        !PAYMENT_GATEWAY_VERIFY_URL
    ) {
        console.error("Missing Payment Gateway environment variables (MERCHANT_ID, API_KEY, PASSP, VERIFY_URL).");
        return { success: false, error: TEXTS.PAYMENT_GATEWAY_CONFIG_ERROR };
    }

    try {
        const { Order: gatewayOrderId, Id: transactionId, CCode: confirmationCode, Amount: amount } = paymentParams;

        if (
            !gatewayOrderId ||
            !transactionId ||
            typeof confirmationCode === "undefined" ||
            typeof amount === "undefined"
        ) {
            console.error("Missing essential parameters from payment gateway redirect.", paymentParams);
            return { success: false, error: TEXTS.PAYMENT_MISSING_PARAMS };
        }

        const supabase = await createClientFromRequest();
        const { data: existingTransaction, error: transactionError } = await supabase
            .from("user_subscriptions")
            .select("id, transaction_id")
            .eq("transaction_id", transactionId)
            .single();

        if (!transactionError && existingTransaction) {
            return {
                success: true,
                message: TEXTS.PAYMENT_VERIFIED_SUCCESS,
                orderId: gatewayOrderId,
                transactionId: transactionId,
                verificationDetails: { alreadyProcessed: true }
            };
        }

        const verificationApiParams: Record<string, string | number> = {
            action: "APISign",
            What: "VERIFY",
            Masof: PAYMENT_GATEWAY_MERCHANT_ID,
            KEY: PAYMENT_GATEWAY_API_KEY,
            PassP: PAYMENT_GATEWAY_PASSP,
            ...paymentParams
        };

        const verifyUrl = new URL(PAYMENT_GATEWAY_VERIFY_URL);
        Object.entries(verificationApiParams).forEach(([key, value]) => {
            verifyUrl.searchParams.append(key, String(value));
        });

        const verifyResponse = await fetch(verifyUrl.toString(), {
            method: "GET"
        });

        const verificationResultString = await verifyResponse.text();

        if (!verifyResponse.ok) {
            const errorCode = extractCCode(verificationResultString);
            const friendlyError = getPaymentErrorMessage(errorCode);
            console.error(
                `Payment Gateway verification request failed: ${verifyResponse.status}. CCode: ${errorCode || "N/A"}. Response: ${verificationResultString.substring(0, 500)}`
            );
            throw new Error(friendlyError);
        }

        const verificationResult = await parseQueryString(verificationResultString);

        const verifiedCCode = verificationResult.CCode ? parseInt(verificationResult.CCode, 10) : -1;

        const couponCode = await extractCouponFromOrderId(gatewayOrderId);
        const originalOrderId = couponCode
            ? gatewayOrderId.substring(0, gatewayOrderId.indexOf("_COUPON_"))
            : gatewayOrderId;

        if (verifiedCCode === 0) {
            const planId = extractPlanIdFromOrderId(originalOrderId);

            const { userId } = await auth();

            if (!userId) {
                console.error("No authenticated user found when verifying payment:", userId);
                return {
                    success: false,
                    error: "Authentication required to complete subscription",
                    orderId: originalOrderId,
                    transactionId: transactionId
                };
            }

            const paymentDetails: PaymentDetails = {
                orderId: originalOrderId,
                transactionId: transactionId,
                amount: Number(amount),
                cCode: confirmationCode,
                aCode: paymentParams.ACode,
                sign: paymentParams.Sign,
                fild1: paymentParams.Fild1,
                fild2: paymentParams.Fild2,
                fild3: paymentParams.Fild3,

                rawParams: { ...paymentParams }
            };

            const updateResult = await updateUserSubscription(userId, planId, paymentDetails, couponCode || undefined);

            if (!updateResult.success) {
                console.error("Failed to update user subscription after payment verification:", updateResult.error);
                return {
                    success: false,
                    error: updateResult.error || "Failed to update subscription",
                    orderId: originalOrderId,
                    transactionId: transactionId
                };
            }

            return {
                success: true,
                message: TEXTS.PAYMENT_VERIFIED_SUCCESS,
                orderId: originalOrderId,
                transactionId: transactionId,
                verificationDetails: verificationResult
            };
        } else {
            const friendlyError = getPaymentErrorMessage(verifiedCCode);
            console.error(
                `Payment Verification Failed for Order ID: ${originalOrderId}. Gateway Verification CCode: ${verifiedCCode}`,
                verificationResult
            );

            return {
                success: false,
                error: friendlyError,
                orderId: originalOrderId,
                verificationDetails: {
                    ...verificationResult,
                    verifiedCCode: verifiedCCode
                }
            };
        }
    } catch (error) {
        console.error("Payment verification error:", error);
        const errorMessage = error instanceof Error ? error.message : TEXTS.PAYMENT_VERIFICATION_ERROR;
        return { success: false, error: errorMessage };
    }
}

export async function applyFreePlan(
    _unused: string,
    planId: string,
    couponCode?: string
): Promise<{ success: boolean; error?: string }> {
    try {
        const { userId } = await auth();
        if (!userId) {
            return { success: false, error: "User not authenticated" };
        }

        const result = await updateUserSubscription(userId, planId, undefined, couponCode);

        if (result.success) {
            revalidatePath("/dashboard");
            revalidatePath("/account");
        }

        return result;
    } catch (error) {
        return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
}
