"use server";

import { ScholarshipConditionGroup } from "@/hooks/use-scholarship-condition-groups";
import { createClientFromRequest } from "@/utils/supabase/server";

interface ActionResult<T> {
    success: boolean;
    data?: T;
    error?: string;
}

export async function getScholarshipConditionGroups(): Promise<ActionResult<ScholarshipConditionGroup[]>> {
    try {
        const supabase = await createClientFromRequest();

        const { data: groups, error: groupsError } = await supabase.from("groups_condition").select("*").order("name");

        if (groupsError) {
            throw groupsError;
        }

        const { data: counts, error: countsError } = await supabase.from("conditions").select("group_id");

        if (countsError) {
            throw countsError;
        }

        const countMap: Record<string, number> = {};
        (counts || []).forEach((item: { group_id: string }) => {
            const groupId = item.group_id;
            countMap[groupId] = (countMap[groupId] || 0) + 1;
        });

        const allGroups = (groups || []).map((group: Omit<ScholarshipConditionGroup, "conditions_count">) => ({
            ...group,
            conditions_count: countMap[group.id] || 0
        }));

        return {
            success: true,
            data: allGroups
        };
    } catch (error) {
        console.error("Error fetching scholarship condition groups:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Failed to fetch scholarship condition groups"
        };
    }
}

export async function deleteScholarshipConditionGroup(id: string): Promise<ActionResult<void>> {
    try {
        const supabase = await createClientFromRequest();

        const { error } = await supabase.from("groups_condition").delete().eq("id", id);

        if (error) {
            throw error;
        }

        return { success: true };
    } catch (error) {
        console.error("Error deleting scholarship condition group:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Failed to delete scholarship condition group"
        };
    }
}
