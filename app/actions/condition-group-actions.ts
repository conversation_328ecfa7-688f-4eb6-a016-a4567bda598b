"use server";

import { revalidatePath } from "next/cache";

import {
    type ConditionGroupDependency,
    type ConditionGroupFormValues,
    type ConditionQuestion,
    TEXTS
} from "@/lib/condition-group-constants";
import { type Tables, type TablesInsert, type TablesUpdate } from "@/types/database.types";
import { mapIdToOption } from "@/utils/form-utils";
import { sanitizeText } from "@/utils/sanitization";
import { createClientFromRequest } from "@/utils/supabase/server";

export interface ConditionGroup extends Tables<"groups_condition"> {
    conditions_count: number;
}

export async function getConditionGroups(): Promise<{ success: boolean; data?: ConditionGroup[]; error?: string }> {
    try {
        const supabase = await createClientFromRequest();

        const { data: groups, error: groupsError } = await supabase.from("groups_condition").select("*").order("name");

        if (groupsError) {
            throw groupsError;
        }

        const { data: conditions, error: conditionsError } = await supabase.from("conditions").select("group_id");

        if (conditionsError) {
            throw conditionsError;
        }

        const countMap: Record<string, number> = {};
        (conditions || []).forEach((item: { group_id: string }) => {
            const groupId = item.group_id;
            countMap[groupId] = (countMap[groupId] || 0) + 1;
        });

        const allGroups: ConditionGroup[] = (groups || []).map((group: Tables<"groups_condition">) => ({
            ...group,
            conditions_count: countMap[group.id] || 0
        }));

        return {
            success: true,
            data: allGroups
        };
    } catch (error) {
        console.error("Error fetching condition groups:", error);
        return {
            success: false,
            error: TEXTS.CONDITION_GROUPS_FETCH_ERROR
        };
    }
}

export async function getConditionGroup(
    id: string
): Promise<{ success: boolean; data?: Tables<"groups_condition">; error?: string }> {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase.from("groups_condition").select("*").eq("id", id).single();

        if (error) {
            throw error;
        }

        if (!data) {
            return { success: false, error: TEXTS.CONDITION_GROUP_NOT_FOUND };
        }

        return {
            success: true,
            data
        };
    } catch (error) {
        console.error("Error fetching condition group:", error);
        return {
            success: false,
            error: TEXTS.CONDITION_GROUP_FETCH_ERROR
        };
    }
}

export async function getPersonalDetailsQuestions(): Promise<{
    success: boolean;
    data?: ConditionQuestion[];
    error?: string;
}> {
    try {
        const supabase = await createClientFromRequest();
        const { data: questionsData, error: questionsError } = await supabase
            .from("questions")
            .select(
                `
                id,
                type,
                metadata,
                groups_question (
                    id,
                    name
                )
            `
            )
            .eq("section", "personal_details")
            .order("created_at", { ascending: false });

        if (questionsError) {
            throw questionsError;
        }

        const formattedQuestions: ConditionQuestion[] = [];

        (questionsData || []).forEach((q) => {
            let rawGroup: { id: unknown; name: unknown } | null = null;
            if (Array.isArray(q.groups_question) && q.groups_question.length > 0) {
                rawGroup = q.groups_question[0];
            } else if (
                q.groups_question &&
                typeof q.groups_question === "object" &&
                !Array.isArray(q.groups_question)
            ) {
                rawGroup = q.groups_question as { id: unknown; name: unknown };
            }

            let questionGroup = undefined;
            if (rawGroup) {
                questionGroup = {
                    id: typeof rawGroup.id === "string" ? rawGroup.id : String(rawGroup.id),
                    name: typeof rawGroup.name === "string" ? rawGroup.name : String(rawGroup.name)
                };
            }

            formattedQuestions.push({
                id: q.id as string,
                type: q.type as string,
                metadata: q.metadata || {},
                groups_question: questionGroup
            });
        });

        return {
            success: true,
            data: formattedQuestions
        };
    } catch (error) {
        console.error("Error fetching questions:", error);
        return {
            success: false,
            error: TEXTS.QUESTIONS_FETCH_ERROR
        };
    }
}

export async function getConditionGroupWithDependencies(id: string): Promise<{
    success: boolean;
    data?: { group: Tables<"groups_condition">; dependencies: ConditionGroupDependency[] };
    error?: string;
}> {
    try {
        const supabase = await createClientFromRequest();

        const { data: conditionGroup, error: groupError } = await supabase
            .from("groups_condition")
            .select("*")
            .eq("id", id)
            .single();

        if (groupError) {
            throw groupError;
        }

        if (!conditionGroup) {
            return { success: false, error: TEXTS.CONDITION_GROUP_NOT_FOUND };
        }

        const { data: conditionsData, error: conditionsError } = await supabase
            .from("conditions")
            .select("*")
            .eq("group_id", id);

        if (conditionsError) {
            throw conditionsError;
        }

        const questionsResult = await getPersonalDetailsQuestions();
        if (!questionsResult.success || !questionsResult.data) {
            return { success: false, error: questionsResult.error || TEXTS.QUESTIONS_FETCH_ERROR };
        }

        const questions = questionsResult.data;

        const dependencies = (conditionsData || []).map((condition) => {
            const questionOption = mapIdToOption(
                condition.question_id,
                questions,
                (question) => question.id,
                (question) => {
                    const metadata = question.metadata as { label?: string; placeholder?: string } | undefined;
                    return metadata?.label || metadata?.placeholder;
                },
                (id) => `Question ID: ${id}`
            );

            return {
                id: condition.id,
                question_id: questionOption,
                condition_type: condition.type,
                condition_value: condition.value
            };
        });

        return {
            success: true,
            data: {
                group: conditionGroup,
                dependencies
            }
        };
    } catch (error) {
        console.error("Error fetching condition group with dependencies:", error);
        return {
            success: false,
            error: TEXTS.CONDITION_GROUP_FETCH_ERROR
        };
    }
}

export async function createConditionGroup(
    data: ConditionGroupFormValues
): Promise<{ success: boolean; error?: string }> {
    try {
        const sanitizedName = data.name ? sanitizeText(data.name, 100) : "";
        if (!sanitizedName?.trim()) {
            return {
                success: false,
                error: TEXTS.nameRequired
            };
        }

        const supabase = await createClientFromRequest();

        const conditionGroupData: TablesInsert<"groups_condition"> = {
            name: sanitizedName.trim()
        };

        const { data: newGroup, error: groupError } = await supabase
            .from("groups_condition")
            .insert(conditionGroupData)
            .select()
            .single();

        if (groupError) {
            throw groupError;
        }

        if (newGroup && data.dependencies.length > 0) {
            const saveResult = await saveConditions(newGroup.id, data.dependencies);
            if (!saveResult.success) {
                return saveResult;
            }
        }

        revalidatePath("/admin/scholarships/conditions/groups");
        return {
            success: true
        };
    } catch (error) {
        console.error("Error creating condition group:", error);
        return {
            success: false,
            error: TEXTS.CONDITION_GROUP_CREATE_ERROR
        };
    }
}

export async function updateConditionGroup(
    id: string,
    data: ConditionGroupFormValues
): Promise<{ success: boolean; error?: string }> {
    try {
        const sanitizedName = data.name ? sanitizeText(data.name, 100) : "";
        if (!sanitizedName?.trim()) {
            return {
                success: false,
                error: TEXTS.nameRequired
            };
        }

        const supabase = await createClientFromRequest();

        const conditionGroupData: TablesUpdate<"groups_condition"> = {
            name: sanitizedName.trim()
        };

        const { error: groupError } = await supabase.from("groups_condition").update(conditionGroupData).eq("id", id);

        if (groupError) {
            throw groupError;
        }

        const { error: deleteError } = await supabase.from("conditions").delete().eq("group_id", id);

        if (deleteError) {
            throw deleteError;
        }

        if (data.dependencies.length > 0) {
            const saveResult = await saveConditions(id, data.dependencies);
            if (!saveResult.success) {
                return saveResult;
            }
        }

        revalidatePath("/admin/scholarships/conditions/groups");
        return {
            success: true
        };
    } catch (error) {
        console.error("Error updating condition group:", error);
        return {
            success: false,
            error: TEXTS.CONDITION_GROUP_UPDATE_ERROR
        };
    }
}

export async function deleteConditionGroup(id: string): Promise<{ success: boolean; error?: string }> {
    try {
        const supabase = await createClientFromRequest();

        const { error: conditionsError } = await supabase.from("conditions").delete().eq("group_id", id);

        if (conditionsError) {
            throw conditionsError;
        }

        const { error: groupError } = await supabase.from("groups_condition").delete().eq("id", id);

        if (groupError) {
            throw groupError;
        }

        revalidatePath("/admin/scholarships/conditions/groups");
        return {
            success: true
        };
    } catch (error) {
        console.error("Error deleting condition group:", error);
        return {
            success: false,
            error: TEXTS.CONDITION_GROUP_DELETE_ERROR
        };
    }
}

async function saveConditions(
    groupId: string,
    dependencies: ConditionGroupDependency[]
): Promise<{ success: boolean; error?: string }> {
    try {
        const supabase = await createClientFromRequest();

        const questionsResult = await getPersonalDetailsQuestions();
        if (!questionsResult.success || !questionsResult.data) {
            return { success: false, error: questionsResult.error || TEXTS.QUESTIONS_FETCH_ERROR };
        }

        const questions = questionsResult.data;

        const conditionsToInsert = dependencies
            .filter((dep) => dep.question_id && dep.question_id.id)
            .map((dep) => {
                const questionId = dep.question_id.id;
                const question = questions.find((q) => q.id === questionId);

                let correctConditionType = "in";
                if (question) {
                    switch (question.type) {
                        case "number_input":
                            correctConditionType = "range";
                            break;
                        case "date_picker":
                            correctConditionType = "date_range";
                            break;
                        case "single_select":
                        case "multi_select":
                        default:
                            correctConditionType = "in";
                            break;
                    }
                }

                let formattedValue: unknown;
                switch (correctConditionType) {
                    case "range":
                        if (typeof dep.condition_value === "object" && !Array.isArray(dep.condition_value)) {
                            formattedValue = dep.condition_value;
                        } else {
                            formattedValue = { min: undefined, max: undefined };
                        }
                        break;
                    case "date_range":
                        if (
                            typeof dep.condition_value === "object" &&
                            !Array.isArray(dep.condition_value) &&
                            dep.condition_value !== null &&
                            "operator" in dep.condition_value &&
                            typeof dep.condition_value.operator === "string"
                        ) {
                            const dateRangeValue = dep.condition_value as {
                                operator: string;
                                days_from_today?: number;
                            };
                            formattedValue = {
                                operator: dateRangeValue.operator || "greater_than",
                                days_from_today: dateRangeValue.days_from_today || 0
                            };
                        } else {
                            formattedValue = { operator: "greater_than", days_from_today: 0 };
                        }
                        break;
                    case "in":
                        formattedValue = Array.isArray(dep.condition_value) ? dep.condition_value : [];
                        break;
                    default:
                        formattedValue = [];
                }

                return {
                    group_id: groupId,
                    question_id: questionId,
                    type: correctConditionType,
                    value: formattedValue
                };
            });

        if (conditionsToInsert.length > 0) {
            const { error: insertError } = await supabase.from("conditions").insert(conditionsToInsert);

            if (insertError) {
                throw insertError;
            }
        }

        return { success: true };
    } catch (error) {
        console.error("Error saving conditions:", error);
        return {
            success: false,
            error: TEXTS.CONDITION_GROUP_CREATE_ERROR
        };
    }
}
