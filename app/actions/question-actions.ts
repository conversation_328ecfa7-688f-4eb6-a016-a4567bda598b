"use server";

import { Database, Tables } from "@/types/database.types";
import { createClientFromRequest } from "@/utils/supabase/server";

const TEXTS = {
    QUESTION_DELETE_ERROR: "שגיאה במחיקת השאלה. אנא נסה שנית.",
    QUESTION_DELETE_SUCCESS: "השאלה נמחקה בהצלחה.",
    QUESTION_CREATE_ERROR: "שגיאה ביצירת השאלה. אנא נסה שנית.",
    QUESTION_UPDATE_ERROR: "שגיאה בעדכון השאלה. אנא נסה שנית.",
    QUESTION_FETCH_ERROR: "שגיאה בטעינת השאלה. אנא נסה שנית."
};

type QuestionMetadata = {
    label?: string;
    required?: boolean;
    tooltip?: string;
    placeholder?: string;
    options?: string[] | Array<{ id: string; label: string }>;
    min?: number;
    max?: number;
    isFutureAllowed?: boolean;
    showSearch?: boolean;
    pattern?: string;
    patternMessage?: string;
};

type ConditionValue =
    | string
    | string[]
    | number
    | { min?: number; max?: number }
    | { operator: "greater_than" | "less_than"; days_from_today: number };

interface DependencyFormData {
    question_id: string;
    condition_type?: string;
    condition_value?: ConditionValue;
}

export interface QuestionFormData {
    type: Database["public"]["Enums"]["question_type"];
    group_id: string;
    section: Database["public"]["Enums"]["question_section"];
    metadata: QuestionMetadata;
    scholarship_ids?: string[];
    dependencies?: DependencyFormData[];
}

interface QuestionWithRelations {
    question: Tables<"questions">;
    scholarshipIds: string[];
    dependencies: DependencyFormData[];
    groupInfo?: { id: string; name: string };
}

const createConditionsForQuestion = async (questionId: string, dependencies: DependencyFormData[]) => {
    const supabase = await createClientFromRequest();

    try {
        const dependenciesJson = dependencies.map((dep) => ({
            question_id: dep.question_id,
            condition_type: dep.condition_type || "in",
            condition_value: dep.condition_value
        }));

        const { data, error } = await supabase.rpc("create_question_conditions", {
            p_question_id: questionId,
            p_dependencies: dependenciesJson
        });

        if (error) throw error;

        const typedData = data as { success: boolean; error?: string };
        if (!typedData?.success) {
            throw new Error(typedData?.error || "Failed to create conditions");
        }

        return data;
    } catch (error) {
        console.error("Error in createConditionsForQuestion:", error);
        throw error;
    }
};

const deleteExistingConditions = async (questionId: string) => {
    const supabase = await createClientFromRequest();

    try {
        const { data, error } = await supabase.rpc("delete_question_conditions", {
            p_question_id: questionId
        });

        if (error) throw error;

        const typedData = data as { success: boolean; error?: string };
        if (!typedData?.success) {
            throw new Error(typedData?.error || "Failed to delete conditions");
        }

        return data;
    } catch (error) {
        console.error("Error deleting existing conditions:", error);
        throw error;
    }
};

const getDependencies = async (questionId: string): Promise<DependencyFormData[]> => {
    const supabase = await createClientFromRequest();

    try {
        const { data: conditionLinks, error: conditionLinksError } = await supabase
            .from("link_question_to_condition")
            .select("condition_id")
            .eq("question_id", questionId);

        if (conditionLinksError) throw conditionLinksError;

        if (!conditionLinks || conditionLinks.length === 0) {
            return [];
        }

        const conditionIds = conditionLinks.map((link) => link.condition_id);

        const { data: conditions, error: conditionsError } = await supabase
            .from("conditions")
            .select("*")
            .in("id", conditionIds);

        if (conditionsError) throw conditionsError;

        return conditions.map((condition): DependencyFormData => {
            const formDep: DependencyFormData = {
                question_id: condition.question_id,
                condition_type: condition.type,
                condition_value: condition.value as ConditionValue
            };

            return formDep;
        });
    } catch (error) {
        console.error("Error getting dependencies:", error);
        return [];
    }
};

export const createQuestion = async (
    questionData: QuestionFormData
): Promise<{ success: boolean; data?: Pick<Tables<"questions">, "id">; error?: string }> => {
    try {
        const supabase = await createClientFromRequest();

        const { scholarship_ids, dependencies, ...questionInsertData } = questionData;

        const { data: insertedQuestion, error } = await supabase
            .from("questions")
            .insert([questionInsertData])
            .select("id")
            .single();

        if (error) throw error;

        if (insertedQuestion && scholarship_ids && scholarship_ids.length > 0) {
            const scholarshipQuestions = scholarship_ids.map((scholarshipId) => ({
                scholarship_id: scholarshipId,
                question_id: insertedQuestion.id
            }));

            const { error: scholarshipError } = await supabase
                .from("link_question_to_scholarship")
                .insert(scholarshipQuestions);

            if (scholarshipError) {
                console.error("Error associating scholarships:", scholarshipError);
            }
        }

        if (insertedQuestion && dependencies && dependencies.length > 0) {
            await createConditionsForQuestion(insertedQuestion.id, dependencies);
        }

        return { success: true, data: insertedQuestion };
    } catch (err) {
        console.error("Error creating question:", err);
        return {
            success: false,
            error: err instanceof Error ? err.message : TEXTS.QUESTION_CREATE_ERROR
        };
    }
};

export const updateQuestion = async (
    questionId: string,
    questionData: QuestionFormData
): Promise<{ success: boolean; error?: string }> => {
    try {
        const supabase = await createClientFromRequest();

        const { scholarship_ids, dependencies, ...questionUpdateData } = questionData;

        const { error } = await supabase.from("questions").update(questionUpdateData).eq("id", questionId);

        if (error) throw error;

        if (scholarship_ids !== undefined) {
            const { error: deleteError } = await supabase
                .from("link_question_to_scholarship")
                .delete()
                .eq("question_id", questionId);

            if (deleteError) {
                console.error("Error removing existing scholarship associations:", deleteError);
            } else if (scholarship_ids.length > 0) {
                const scholarshipQuestions = scholarship_ids.map((scholarshipId) => ({
                    scholarship_id: scholarshipId,
                    question_id: questionId
                }));

                const { error: scholarshipError } = await supabase
                    .from("link_question_to_scholarship")
                    .insert(scholarshipQuestions);

                if (scholarshipError) {
                    console.error("Error associating scholarships:", scholarshipError);
                }
            }
        }

        if (dependencies !== undefined) {
            await deleteExistingConditions(questionId);

            if (dependencies.length > 0) {
                await createConditionsForQuestion(questionId, dependencies);
            }
        }

        return { success: true };
    } catch (err) {
        console.error("Error updating question:", err);
        return {
            success: false,
            error: err instanceof Error ? err.message : TEXTS.QUESTION_UPDATE_ERROR
        };
    }
};

export const getQuestion = async (
    questionId: string
): Promise<{ success: boolean; data?: QuestionWithRelations; error?: string }> => {
    try {
        const supabase = await createClientFromRequest();

        const { data: questionData, error: questionError } = await supabase
            .from("questions")
            .select("*")
            .eq("id", questionId)
            .single();

        if (questionError) throw questionError;

        const { data: scholarshipData, error: scholarshipError } = await supabase
            .from("link_question_to_scholarship")
            .select("scholarship_id")
            .eq("question_id", questionId);

        if (scholarshipError) throw scholarshipError;

        let groupInfo: { id: string; name: string } | undefined;
        if (questionData.group_id) {
            const { data: groupData, error: groupError } = await supabase
                .from("groups_question")
                .select("id, name")
                .eq("id", questionData.group_id)
                .single();

            if (!groupError && groupData) {
                groupInfo = groupData;
            }
        }

        const dependencies = await getDependencies(questionId);

        return {
            success: true,
            data: {
                question: questionData,
                scholarshipIds: scholarshipData?.map((item) => item.scholarship_id) || [],
                dependencies,
                groupInfo
            }
        };
    } catch (err) {
        console.error("Error fetching question:", err);
        return {
            success: false,
            error: err instanceof Error ? err.message : TEXTS.QUESTION_FETCH_ERROR
        };
    }
};

export const getQuestionFormData = async (): Promise<{
    success: boolean;
    data?: {
        scholarships: Array<{ id: string; title: string }>;
        availableQuestions: Array<{
            id: string;
            type: Database["public"]["Enums"]["question_type"];
            metadata: QuestionMetadata;
            groups_question: { id: string; name: string } | Array<{ id: string; name: string }>;
        }>;
    };
    error?: string;
}> => {
    try {
        const supabase = await createClientFromRequest();

        const [scholarshipsRes, questionsRes] = await Promise.all([
            supabase
                .from("scholarships")
                .select("id, title")
                .eq("is_active", true)
                .eq("is_public", true)
                .order("title"),
            supabase
                .from("questions")
                .select(
                    `
                id,
                type,
                metadata,
                group_id,
                groups_question!inner (
                    id,
                    name
                )
            `
                )
                .not("type", "in", `(short_text,long_text,address_select,bank_select)`)
                .order("created_at", { ascending: false })
        ]);

        if (scholarshipsRes.error) throw scholarshipsRes.error;
        if (questionsRes.error) {
            if (
                questionsRes.error.code === "22P02" &&
                questionsRes.error.message?.includes("invalid input value for enum question_type")
            ) {
                console.warn("Invalid question type found in database, filtering with different approach");

                const fallbackQuestionsRes = await supabase
                    .from("questions")
                    .select(
                        `
                    id,
                    type,
                    metadata,
                    group_id,
                    groups_question!inner (
                        id,
                        name
                    )
                `
                    )
                    .order("created_at", { ascending: false });

                if (fallbackQuestionsRes.error) throw fallbackQuestionsRes.error;

                const excludedTypes = ["short_text", "long_text", "address_select", "bank_select"];
                const validQuestions = (fallbackQuestionsRes.data || []).filter((q) => {
                    try {
                        return q.type && !excludedTypes.includes(q.type);
                    } catch {
                        return false;
                    }
                });

                const processedQuestions = validQuestions.map((q) => ({
                    id: q.id,
                    type: q.type,
                    metadata: q.metadata as QuestionMetadata,
                    groups_question: Array.isArray(q.groups_question) ? q.groups_question[0] : q.groups_question
                }));

                return {
                    success: true,
                    data: {
                        scholarships: scholarshipsRes.data || [],
                        availableQuestions: processedQuestions
                    }
                };
            }
            throw questionsRes.error;
        }

        const processedQuestions = (questionsRes.data || []).map((q) => ({
            id: q.id,
            type: q.type,
            metadata: q.metadata as QuestionMetadata,
            groups_question: Array.isArray(q.groups_question) ? q.groups_question[0] : q.groups_question
        }));

        return {
            success: true,
            data: {
                scholarships: scholarshipsRes.data || [],
                availableQuestions: processedQuestions
            }
        };
    } catch (err) {
        console.error("Error fetching question form data:", err);
        return {
            success: false,
            error: err instanceof Error ? err.message : "שגיאה בטעינת נתוני הטופס"
        };
    }
};

export const deleteQuestion = async (id: string): Promise<{ success: boolean; error?: string }> => {
    try {
        const supabase = await createClientFromRequest();

        await deleteExistingConditions(id);

        try {
            const { error: depsError1 } = await supabase.from("link_question_to_condition").delete().eq("question_id", id);

            if (depsError1 && depsError1.code !== "42P01") {
                throw depsError1;
            }

            const { error: depsError2 } = await supabase
                .from("link_question_to_condition")
                .delete()
                .eq("dependent_question_id", id);

            if (depsError2 && depsError2.code !== "42P01") {
                throw depsError2;
            }
        } catch (depsErr) {
            if (
                depsErr instanceof Error &&
                !depsErr.message.includes('relation "public.question_dependencies" does not exist')
            ) {
                throw depsErr;
            }

            console.warn("question_dependencies table does not exist, skipping dependency deletion");
        }

        const { error: scholarshipError } = await supabase
            .from("link_question_to_scholarship")
            .delete()
            .eq("question_id", id);

        if (scholarshipError) throw scholarshipError;

        const { error: questionError } = await supabase.from("questions").delete().eq("id", id);

        if (questionError) throw questionError;

        return { success: true };
    } catch (err) {
        console.error("Error deleting question:", err);
        return {
            success: false,
            error: err instanceof Error ? err.message : TEXTS.QUESTION_DELETE_ERROR
        };
    }
};
