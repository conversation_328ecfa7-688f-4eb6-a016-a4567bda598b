"use server";

import type { Database } from "@/types/database.types";
import { createClientFromRequest } from "@/utils/supabase/server";

type Collaboration = Database["public"]["Tables"]["collaborations"]["Row"];
type Answer = Database["public"]["Tables"]["answers"]["Row"];
type Question = Database["public"]["Tables"]["questions"]["Row"];
type Condition = Database["public"]["Tables"]["conditions"]["Row"] & {
    condition?: {
        condition_type: "equals" | "in" | "not" | "greater_than" | "less_than";
        condition_value: string | string[] | number;
    };
    type?: Database["public"]["Enums"]["condition_type"] | "greater_than" | "less_than";
    value?:
        | {
              selected_options?: string[];
              min?: number;
              max?: number;
              operator?: "greater_than" | "less_than";
              days_from_today?: number;
          }
        | number;
};

interface SendCollaborationResult {
    success: boolean;
    error?: string;
    collaborationsSent: {
        collaborationId: string;
        statusCode: number;
    }[];
}

async function checkConditionMet(condition: Condition, answers: Answer[]): Promise<boolean> {
    const answerForQuestion = answers.find((a) => a.question_id === condition.question_id);

    if (!answerForQuestion) {
        return false;
    }

    const answerValue = answerForQuestion.answer;

    if (condition.condition) {
        const { condition_type, condition_value } = condition.condition;

        switch (condition_type) {
            case "equals":
                return String(answerValue) === String(condition_value);

            case "in":
                if (Array.isArray(condition_value)) {
                    return condition_value.includes(answerValue);
                }
                return false;
            case "not":
                return answerValue !== condition_value;
            case "greater_than":
                return (
                    typeof answerValue === "string" &&
                    typeof condition_value === "number" &&
                    parseFloat(answerValue) > condition_value
                );
            case "less_than":
                return (
                    typeof answerValue === "string" &&
                    typeof condition_value === "number" &&
                    parseFloat(answerValue) < condition_value
                );
            default:
                return false;
        }
    } else if (condition.type) {
        if (condition.type === "in" && condition.value && typeof condition.value === "object") {
            if ("selected_options" in condition.value) {
                const selectedOptions = condition.value.selected_options || [];
                return Array.isArray(selectedOptions) && selectedOptions.includes(answerValue);
            } else if (Array.isArray(condition.value)) {
                return condition.value.includes(answerValue);
            }
            return false;
        } else if (condition.type === "range" && condition.value && typeof condition.value === "object") {
            const min = condition.value.min;
            const max = condition.value.max;
            const numValue = parseFloat(answerValue);

            if (isNaN(numValue)) return false;

            if (min !== undefined && max !== undefined) {
                return numValue >= min && numValue <= max;
            } else if (min !== undefined) {
                return numValue >= min;
            } else if (max !== undefined) {
                return numValue <= max;
            }
            return true;
        } else if (condition.type === "date_range" && condition.value && typeof condition.value === "object") {
            const dateValue = new Date(answerValue);

            if (isNaN(dateValue.getTime())) return false;

            if ("operator" in condition.value && "days_from_today" in condition.value) {
                const operator = condition.value.operator as "greater_than" | "less_than";
                const daysFromToday = Number(condition.value.days_from_today);

                if (isNaN(daysFromToday)) return false;

                const today = new Date();
                const diffTime = Math.abs(dateValue.getTime() - today.getTime());
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                return operator === "greater_than" ? diffDays > daysFromToday : diffDays < daysFromToday;
            }
        }
    }

    return true;
}

async function getCollaborationsToSend(userId: string): Promise<{
    collaborations: Collaboration[];
    questions: Record<string, Question>;
    answers: Answer[];
    conditions: Record<string, Condition[]>;
}> {
    const supabase = await createClientFromRequest();

    const { data: answers, error: answersError } = await supabase.from("answers").select("*").eq("user_id", userId);

    if (answersError) {
        throw new Error(`Error fetching answers: ${answersError.message}`);
    }

    if (!answers || answers.length === 0) {
        return { collaborations: [], questions: {}, answers: [], conditions: {} };
    }

    const questionIds = Array.from(new Set(answers.map((a: Answer) => a.question_id)));

    const { data: questionCollaborationLinks, error: linksError } = await supabase
        .from("link_question_to_collaboration")
        .select("collaboration_id, question_id")
        .in("question_id", questionIds);

    if (linksError) {
        throw new Error(`Error fetching question-collaboration links: ${linksError.message}`);
    }

    if (!questionCollaborationLinks || questionCollaborationLinks.length === 0) {
        return { collaborations: [], questions: {}, answers: [], conditions: {} };
    }

    const collaborationIds = Array.from(new Set(questionCollaborationLinks.map((link) => link.collaboration_id)));

    const { data: collaborations, error: collaborationsError } = await supabase
        .from("collaborations")
        .select("*")
        .in("id", collaborationIds);

    if (collaborationsError) {
        throw new Error(`Error fetching collaborations: ${collaborationsError.message}`);
    }

    const { data: collaborationConditionLinks, error: conditionLinksError } = await supabase
        .from("link_collaboration_to_condition")
        .select("collaboration_id, condition_id")
        .in("collaboration_id", collaborationIds);

    if (conditionLinksError) {
        throw new Error(`Error fetching collaboration-condition links: ${conditionLinksError.message}`);
    }

    const conditionIds = collaborationConditionLinks.map((link) => link.condition_id);

    const { data: conditions, error: conditionsError } = await supabase
        .from("conditions")
        .select("*")
        .in("id", conditionIds);

    if (conditionsError) {
        throw new Error(`Error fetching conditions: ${conditionsError.message}`);
    }

    const conditionsByCollaboration: Record<string, Condition[]> = {};
    collaborationConditionLinks.forEach((link) => {
        if (!conditionsByCollaboration[link.collaboration_id]) {
            conditionsByCollaboration[link.collaboration_id] = [];
        }
        const condition = conditions.find((c) => c.id === link.condition_id);
        if (condition) {
            conditionsByCollaboration[link.collaboration_id].push(condition as Condition);
        }
    });

    const { data: questionsData, error: questionsError } = await supabase
        .from("questions")
        .select("*")
        .in("id", questionIds);

    if (questionsError) {
        throw new Error(`Error fetching questions: ${questionsError.message}`);
    }

    const questions: Record<string, Question> = {};
    questionsData.forEach((q) => {
        questions[q.id] = q;
    });

    return {
        collaborations: collaborations as Collaboration[],
        questions,
        answers: answers as Answer[],
        conditions: conditionsByCollaboration
    };
}

export async function sendCollaborationData(userId: string): Promise<SendCollaborationResult> {
    try {
        const supabase = await createClientFromRequest();
        const { collaborations, questions, answers, conditions } = await getCollaborationsToSend(userId);

        if (collaborations.length === 0) {
            return { success: true, collaborationsSent: [] };
        }

        const collaborationsSent = [];

        for (const collaboration of collaborations) {
            const collaborationConditions = conditions[collaboration.id] || [];

            const allConditionsMet =
                collaborationConditions.length === 0 ||
                (await Promise.all(
                    collaborationConditions.map((condition) => checkConditionMet(condition, answers))
                ).then((results) => results.every((result) => result)));

            if (!allConditionsMet) {
                continue;
            }

            const { data: linkedQuestions, error: linkedQuestionsError } = await supabase
                .from("link_question_to_collaboration")
                .select("question_id")
                .eq("collaboration_id", collaboration.id);

            if (linkedQuestionsError) {
                throw new Error(`Error fetching linked questions: ${linkedQuestionsError.message}`);
            }

            const linkedQuestionIds = linkedQuestions.map((q) => q.question_id);

            let userEmail: string | null | undefined = null;
            let userPhone: string | null | undefined = null;

            try {
                const { data: sessionData } = await supabase.auth.getSession();

                if (sessionData?.session?.user?.id === userId) {
                    userEmail = sessionData.session.user.email;
                    userPhone = sessionData.session.user.phone;
                } else {
                    const { data: profileData, error: profileError } = await supabase
                        .from("profiles")
                        .select("email, phone")
                        .eq("id", userId)
                        .single();

                    if (!profileError && profileData) {
                        userEmail = profileData.email;
                        userPhone = profileData.phone;
                    }
                }
            } catch (error) {
                console.error(`Error fetching user data: ${error instanceof Error ? error.message : String(error)}`);
            }

            const payload: Record<string, unknown> = {
                timestamp: new Date().toISOString(),
                user: {
                    id: userId,
                    email: userEmail,
                    phone: userPhone
                },
                answers: {}
            };

            linkedQuestionIds.forEach((questionId: string) => {
                const answer = answers.find((a) => a.question_id === questionId);
                const question = questions[questionId];

                if (answer && question) {
                    let processedAnswer: string | number | string[] | null = answer.answer;

                    if (question.type === "multi_select") {
                        try {
                            processedAnswer = JSON.parse(answer.answer) as string[];
                        } catch {
                            processedAnswer = answer.answer;
                        }
                    } else if (question.type === "number_input") {
                        const numValue = parseFloat(answer.answer);
                        if (isNaN(numValue)) {
                            processedAnswer = null;
                        } else {
                            processedAnswer = numValue;
                        }
                    }

                    const metadata = question.metadata as Record<string, unknown>;
                    const label = (metadata?.label as string) || questionId;

                    (payload.answers as Record<string, unknown>)[label] = {
                        value: processedAnswer,
                        question_id: questionId,
                        question_type: question.type
                    };
                }
            });

            let response;
            let statusCode = 0;
            let responseBody: unknown = null;

            try {
                const headers: Record<string, string> = {
                    "Content-Type": "application/json"
                };

                if (
                    collaboration.auth_type === "bearer_token" &&
                    collaboration.auth_value &&
                    typeof collaboration.auth_value === "object" &&
                    collaboration.auth_value !== null
                ) {
                    const authValue = collaboration.auth_value as Record<string, unknown>;
                    if ("token" in authValue) {
                        headers["Authorization"] = `Bearer ${authValue.token}`;
                    }
                }

                response = await fetch(collaboration.api_endpoint, {
                    method: "POST",
                    headers,
                    body: JSON.stringify(payload)
                });

                statusCode = response.status;

                try {
                    responseBody = await response.json();
                } catch {
                    responseBody = { text: await response.text() };
                }
            } catch (error) {
                console.error(`Error sending data to collaboration ${collaboration.id}:`, error);

                statusCode = 503;
                responseBody = { error: error instanceof Error ? error.message : "Unknown error" };
            }

            const { error: historyError } = await supabase.from("collaborations_history").insert({
                user_id: userId,
                collaboration_id: collaboration.id,
                request_payload:
                    payload as Database["public"]["Tables"]["collaborations_history"]["Insert"]["request_payload"],
                response_status_code: statusCode,
                response_body:
                    responseBody as Database["public"]["Tables"]["collaborations_history"]["Insert"]["response_body"]
            });

            if (historyError) {
                console.error(`Error recording collaboration history:`, historyError);
            }

            collaborationsSent.push({
                collaborationId: collaboration.id,
                statusCode
            });
        }

        return {
            success: true,
            collaborationsSent
        };
    } catch (error) {
        console.error("Error in sendCollaborationData:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error occurred",
            collaborationsSent: []
        };
    }
}
