"use server";

import { revalidatePath } from "next/cache";

import { TEXTS } from "@/lib/coupon-group-constants";
import { type Tables, type TablesInsert, type TablesUpdate } from "@/types/database.types";
import { createClientFromRequest } from "@/utils/supabase/server";

export interface CouponGroup extends Tables<"groups_coupon"> {
    coupons_count: number;
}

export interface CouponGroupFormValues {
    name: string;
    description: string;
}

export async function getCouponGroups(): Promise<{ success: boolean; data?: CouponGroup[]; error?: string }> {
    try {
        const supabase = await createClientFromRequest();

        const { data: groups, error: groupsError } = await supabase.from("groups_coupon").select("*").order("name");

        if (groupsError) {
            throw groupsError;
        }

        const { data: counts, error: countsError } = await supabase.from("coupons").select("coupon_group_id");

        if (countsError) {
            throw countsError;
        }

        const countMap: Record<string, number> = {};
        (counts || []).forEach((item: { coupon_group_id: string }) => {
            const groupId = item.coupon_group_id;
            countMap[groupId] = (countMap[groupId] || 0) + 1;
        });

        const allGroups: CouponGroup[] = (groups || []).map((group: Tables<"groups_coupon">) => ({
            ...group,
            coupons_count: countMap[group.id] || 0
        }));

        return {
            success: true,
            data: allGroups
        };
    } catch (error) {
        console.error("Error fetching coupon groups:", error);
        return {
            success: false,
            error: TEXTS.couponGroupsFetchError
        };
    }
}

export async function getCouponGroup(
    id: string
): Promise<{ success: boolean; data?: Tables<"groups_coupon">; error?: string }> {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase.from("groups_coupon").select("*").eq("id", id).single();

        if (error) {
            throw error;
        }

        if (!data) {
            return { success: false, error: TEXTS.couponGroupNotFound };
        }

        return {
            success: true,
            data
        };
    } catch (error) {
        console.error("Error fetching coupon group:", error);
        return {
            success: false,
            error: TEXTS.couponGroupFetchError
        };
    }
}

export async function createCouponGroup(data: CouponGroupFormValues): Promise<{ success: boolean; error?: string }> {
    try {
        if (!data.name) {
            return {
                success: false,
                error: TEXTS.nameRequired
            };
        }

        const supabase = await createClientFromRequest();

        const couponGroupData: TablesInsert<"groups_coupon"> = {
            name: data.name,
            description: data.description || null
        };

        const { error } = await supabase.from("groups_coupon").insert(couponGroupData);

        if (error) {
            throw error;
        }

        revalidatePath("/admin/coupons");
        return {
            success: true
        };
    } catch (error) {
        console.error("Error creating coupon group:", error);
        return {
            success: false,
            error: TEXTS.couponGroupCreateError
        };
    }
}

export async function updateCouponGroup(
    id: string,
    data: CouponGroupFormValues
): Promise<{ success: boolean; error?: string }> {
    try {
        if (!data.name) {
            return {
                success: false,
                error: TEXTS.nameRequired
            };
        }

        const supabase = await createClientFromRequest();

        const couponGroupData: TablesUpdate<"groups_coupon"> = {
            name: data.name,
            description: data.description || null
        };

        const { error } = await supabase.from("groups_coupon").update(couponGroupData).eq("id", id);

        if (error) {
            throw error;
        }

        revalidatePath("/admin/coupons");
        return {
            success: true
        };
    } catch (error) {
        console.error("Error updating coupon group:", error);
        return {
            success: false,
            error: TEXTS.couponGroupUpdateError
        };
    }
}

export async function deleteCouponGroup(id: string): Promise<{ success: boolean; error?: string }> {
    try {
        const supabase = await createClientFromRequest();
        const { error } = await supabase.from("groups_coupon").delete().eq("id", id);

        if (error) {
            throw error;
        }

        revalidatePath("/admin/coupons");
        return {
            success: true
        };
    } catch (error) {
        console.error("Error deleting coupon group:", error);
        return {
            success: false,
            error: TEXTS.couponGroupDeleteError
        };
    }
}
