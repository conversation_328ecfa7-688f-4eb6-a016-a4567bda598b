"use server";

import { PostgrestFilterBuilder } from "@supabase/postgrest-js";

import { DateFilterValue, FilterValue, NumberFilterValue } from "@/components/table/types";
import { ScholarshipWithGroups, TEXTS } from "@/lib/scholarship-constants";
import { type Database, type Tables } from "@/types/database.types";
import { createClientFromRequest } from "@/utils/supabase/server";

type ScholarshipFilterBuilder<T> = PostgrestFilterBuilder<Database["public"], Record<string, unknown>, T>;

const ITEMS_PER_PAGE = 5;

function applyFilters<Result>(
    query: ScholarshipFilterBuilder<Result>,
    filters: Record<string, FilterValue>
): ScholarshipFilterBuilder<Result> {
    let filteredQuery = query;

    for (const key in filters) {
        const value = filters[key];

        switch (key) {
            case "title":
                if (typeof value === "string") {
                    filteredQuery = filteredQuery.ilike(key, `%${value}%`);
                }
                break;
            case "amount":
                if (typeof value === "number") {
                    filteredQuery = filteredQuery.eq(key, value);
                } else if (typeof value === "object" && !Array.isArray(value) && value !== null) {
                    const numFilter = value as NumberFilterValue;
                    if (numFilter.min !== undefined) {
                        filteredQuery = filteredQuery.gte(key, numFilter.min);
                    }
                    if (numFilter.max !== undefined) {
                        filteredQuery = filteredQuery.lte(key, numFilter.max);
                    }
                }
                break;
            case "is_public":
                if (Array.isArray(value) && value.length > 0) {
                    const boolValue = typeof value[0] === "string" ? value[0] === "true" : (value[0] as boolean);
                    filteredQuery = filteredQuery.eq(key, boolValue);
                }
                break;
            case "start_date":
            case "end_date":
                if (typeof value === "string") {
                    filteredQuery = filteredQuery.eq(key, value);
                } else if (typeof value === "object" && !Array.isArray(value) && value !== null) {
                    const dateFilter = value as DateFilterValue;
                    if (dateFilter.startDate) {
                        filteredQuery = filteredQuery.gte(key, dateFilter.startDate.toISOString());
                    }
                    if (dateFilter.endDate) {
                        filteredQuery = filteredQuery.lte(key, dateFilter.endDate.toISOString());
                    }
                }
                break;
        }
    }

    return filteredQuery;
}

async function getScholarshipIdsByGroup(
    supabase: ReturnType<typeof createClientFromRequest> extends Promise<infer T> ? T : never,
    groupFilter: string | string[],
    selectOptions?: Record<string, unknown>
): Promise<string[]> {
    let groupQuery = supabase
        .from("link_scholarship_to_scholarship_groups")
        .select("scholarship_id", selectOptions)
        .not("scholarship_id", "is", null);

    if (Array.isArray(groupFilter)) {
        groupQuery = groupQuery.in("scholarship_group_id", groupFilter);
    } else {
        groupQuery = groupQuery.eq("scholarship_group_id", groupFilter);
    }

    const { data: groupScholarshipIds, error } = await groupQuery;
    if (error) throw new Error(`Error fetching group scholarship IDs: ${error.message}`);
    return groupScholarshipIds?.map((g) => g.scholarship_id) || [];
}

export async function getAdminScholarships({
    page = 1,
    pageSize = 10,
    filters = {}
}: {
    page?: number;
    pageSize?: number;
    filters?: Record<string, FilterValue>;
}): Promise<{
    data: ScholarshipWithGroups[];
    count: number | null;
    error?: string;
}> {
    try {
        const supabase = await createClientFromRequest();

        const filtersCopy = { ...filters };
        let groupFilter: string | string[] | null = null;

        if (filtersCopy.group) {
            const groupValue = filtersCopy.group;

            if (Array.isArray(groupValue)) {
                groupFilter = groupValue.map((item) => {
                    if (typeof item === "object" && item !== null && "id" in item) {
                        return (item as { id: string }).id;
                    }
                    return String(item);
                });
            } else if (typeof groupValue === "object" && groupValue !== null && "id" in groupValue) {
                groupFilter = (groupValue as { id: string }).id;
            } else {
                groupFilter = String(groupValue);
            }
            delete filtersCopy.group;
        }

        let countBaseQuery = supabase.from("scholarships").select("id", { count: "exact" });
        // @ts-expect-error - Complex type mismatch between different query result types
        countBaseQuery = applyFilters(countBaseQuery, filtersCopy);

        if (groupFilter) {
            const scholarshipIdsInGroup = await getScholarshipIdsByGroup(supabase, groupFilter, { count: "exact" });
            if (scholarshipIdsInGroup.length > 0) {
                countBaseQuery = countBaseQuery.in("id", scholarshipIdsInGroup);
            } else {
                return { data: [], count: 0 };
            }
        }

        const { count, error: countError } = await countBaseQuery;
        if (countError) throw new Error(`Error counting filtered scholarships: ${countError.message}`);

        if (count === 0) {
            return { data: [], count: 0 };
        }

        const from = (page - 1) * pageSize;
        const to = from + pageSize - 1;

        let dataQuery = supabase.from("scholarships").select(
            `
                *,
                link_scholarship_to_scholarship_groups!left (
                    scholarship_group_id,
                    scholarship_id,
                    created_at,
                    groups_scholarship (
                        title
                    )
                )
            `
        );
        // @ts-expect-error - Complex type mismatch between different query result types
        dataQuery = applyFilters(dataQuery, filtersCopy);

        if (groupFilter) {
            const scholarshipIdsInGroup = await getScholarshipIdsByGroup(supabase, groupFilter);
            if (scholarshipIdsInGroup.length > 0) {
                dataQuery = dataQuery.in("id", scholarshipIdsInGroup);
            } else {
                return { data: [], count: 0 };
            }
        }

        const { data, error: fetchError } = await dataQuery.order("updated_at", { ascending: false }).range(from, to);
        if (fetchError) throw new Error(`Error fetching scholarships data: ${fetchError.message}`);

        return { data: (data as ScholarshipWithGroups[]) || [], count };
    } catch (error) {
        console.error("Error in getAdminScholarships:", error);
        return {
            data: [],
            count: 0,
            error: error instanceof Error ? error.message : TEXTS.ENTITY_FETCH_ERROR
        };
    }
}

export async function getScholarshipsPage(
    page: number,
    groupSlug?: string
): Promise<{ data: Tables<"scholarships">[]; count: number | null; error?: string }> {
    try {
        const supabase = await createClientFromRequest();
        const from = (page - 1) * ITEMS_PER_PAGE;
        const to = from + ITEMS_PER_PAGE - 1;

        let countQuery = supabase
            .from("scholarships")
            .select(
                `
                id,
                link_scholarship_to_scholarship_groups!inner(
                    scholarship_group_id,
                    groups_scholarship!inner(
                        slug
                    )
                )
            `,
                { count: "exact", head: true }
            )
            .eq("is_public", true);

        if (groupSlug) {
            countQuery = countQuery.eq("link_scholarship_to_scholarship_groups.groups_scholarship.slug", groupSlug);
        }

        const { count, error: countError } = await countQuery;

        if (countError) throw countError;

        let query = supabase
            .from("scholarships")
            .select(
                `
                *,
                link_scholarship_to_scholarship_groups!inner(
                    scholarship_group_id,
                    groups_scholarship!inner(
                        slug
                    )
                )
            `
            )
            .eq("is_public", true)
            .range(from, to);

        if (groupSlug) {
            query = query.eq("link_scholarship_to_scholarship_groups.groups_scholarship.slug", groupSlug);
        }

        const { data, error } = await query;

        if (error) throw error;

        return { data: data || [], count: count };
    } catch {
        return { data: [], count: 0, error: TEXTS.SCHOLARSHIPS_FETCH_ERROR };
    }
}

export const deleteScholarship = async (id: string): Promise<{ success: boolean; error?: string }> => {
    const supabase = await createClientFromRequest();

    const { error: testimonialError } = await supabase
        .from("link_scholarship_to_testimonial")
        .delete()
        .eq("scholarship_id", id);

    if (testimonialError) {
        return { success: false, error: TEXTS.SCHOLARSHIP_DELETE_ERROR };
    }

    const { error: groupError } = await supabase
        .from("link_scholarship_to_scholarship_groups")
        .delete()
        .eq("scholarship_id", id);

    if (groupError) {
        return { success: false, error: TEXTS.SCHOLARSHIP_DELETE_ERROR };
    }

    const { error: deleteError } = await supabase.from("scholarships").delete().eq("id", id);

    if (deleteError) {
        return { success: false, error: TEXTS.SCHOLARSHIP_DELETE_ERROR };
    }

    return { success: true };
};
