"use server";

import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";

import { type Tables, type TablesInsert } from "@/types/database.types";
import { sanitizeText } from "@/utils/sanitization";
import { createClientFromRequest } from "@/utils/supabase/server";

export interface UserNoteWithReporter extends Tables<"user_notes"> {
    reporter_email: string | null;
}

export interface UserNotesResult {
    success: boolean;
    notes?: UserNoteWithReporter[];
    error?: string;
}

export interface UserNoteActionResult {
    success: boolean;
    error?: string;
}

const TEXTS = {
    noteRequired: "יש להזין הערה",
    reportedUserIdRequired: "מזהה משתמש נדרש",
    noteCreateError: "שגיאה ביצירת הערה",
    noteDeleteError: "שגיאה במחיקת הערה",
    notesFetchError: "שגי<PERSON><PERSON> בטעינת הערות",
    unauthorized: "אין הרשאה לבצע פעולה זו",
    noteNotFound: "הערה לא נמצאה"
};

export async function createUserNote(reportedUserId: string, note: string): Promise<UserNoteActionResult> {
    try {
        const { userId } = await auth();
        if (!userId) {
            return { success: false, error: TEXTS.unauthorized };
        }

        const sanitizedReportedUserId = sanitizeText(reportedUserId, 100);
        const sanitizedNote = sanitizeText(note, 1000);

        if (!sanitizedReportedUserId?.trim()) {
            return { success: false, error: TEXTS.reportedUserIdRequired };
        }

        if (!sanitizedNote?.trim()) {
            return { success: false, error: TEXTS.noteRequired };
        }

        const supabase = await createClientFromRequest();

        const noteData: TablesInsert<"user_notes"> = {
            user_id: userId,
            reported_user_id: sanitizedReportedUserId.trim(),
            note: sanitizedNote.trim()
        };

        const { error } = await supabase.from("user_notes").insert(noteData);

        if (error) {
            console.error("Error creating user note:", error);
            return { success: false, error: TEXTS.noteCreateError };
        }

        revalidatePath("/admin/users");
        return { success: true };
    } catch (error) {
        console.error("Error in createUserNote:", error);
        return { success: false, error: TEXTS.noteCreateError };
    }
}

export async function deleteUserNote(noteId: string): Promise<UserNoteActionResult> {
    try {
        const { userId } = await auth();
        if (!userId) {
            return { success: false, error: TEXTS.unauthorized };
        }

        const sanitizedNoteId = sanitizeText(noteId, 100);
        if (!sanitizedNoteId?.trim()) {
            return { success: false, error: TEXTS.noteNotFound };
        }

        const supabase = await createClientFromRequest();

        const { error } = await supabase.from("user_notes").delete().eq("id", sanitizedNoteId.trim());

        if (error) {
            console.error("Error deleting user note:", error);
            return { success: false, error: TEXTS.noteDeleteError };
        }

        revalidatePath("/admin/users");
        return { success: true };
    } catch (error) {
        console.error("Error in deleteUserNote:", error);
        return { success: false, error: TEXTS.noteDeleteError };
    }
}

export async function getUserNotes(reportedUserId: string): Promise<UserNotesResult> {
    try {
        const { userId } = await auth();
        if (!userId) {
            return { success: false, error: TEXTS.unauthorized };
        }

        const sanitizedReportedUserId = sanitizeText(reportedUserId, 100);
        if (!sanitizedReportedUserId?.trim()) {
            return { success: false, error: TEXTS.reportedUserIdRequired };
        }

        const supabase = await createClientFromRequest();

        const { data: notes, error } = await supabase
            .from("user_notes")
            .select("*")
            .eq("reported_user_id", sanitizedReportedUserId.trim())
            .order("created_at", { ascending: false });

        if (error) {
            console.error("Error fetching user notes:", error);
            return { success: false, error: TEXTS.notesFetchError };
        }

        const notesWithReporter: UserNoteWithReporter[] = await Promise.all(
            notes.map(async (note) => {
                let reporterEmail: string | null = null;

                try {
                    const { data: claimData } = await supabase
                        .from("user_claims")
                        .select("claim_value")
                        .eq("user_id", note.user_id)
                        .eq("claim_key", "user_email")
                        .order("updated_at", { ascending: false })
                        .limit(1)
                        .single();

                    if (claimData?.claim_value) {
                        reporterEmail = claimData.claim_value as string;
                    }
                } catch (error) {
                    console.error("Error fetching reporter email:", error);
                }

                return {
                    ...note,
                    reporter_email: reporterEmail || note.user_id
                };
            })
        );

        return { success: true, notes: notesWithReporter };
    } catch (error) {
        console.error("Error in getUserNotes:", error);
        return { success: false, error: TEXTS.notesFetchError };
    }
}
