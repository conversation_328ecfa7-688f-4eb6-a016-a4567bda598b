"use server";

import { revalidatePath } from "next/cache";

import { TEXTS } from "@/lib/collaboration-constants";
import { type Database, type Json, type Tables } from "@/types/database.types";
import { createClientFromRequest } from "@/utils/supabase/server";

type AuthType = Database["public"]["Enums"]["auth_type"];

type CollaborationRpcResult =
    | { success: true; collaboration_id: string; condition_ids: string[] }
    | { success: false; error: string; error_code?: string };

function isValidUrl(url: string): boolean {
    try {
        new URL(url);
        return true;
    } catch (error) {
        console.error("Error validating URL:", error);
        return false;
    }
}

interface ConditionDependencyInput {
    question_id: { id: string; label: string } | string;
    condition_type: string;
    condition_value: unknown;
    id?: string;
}

interface CollaborationFormValues {
    name: string;
    description?: string;
    api_endpoint: string;
    auth_type: AuthType;
    auth_value?: string;
    dependencies?: ConditionDependencyInput[];
    question_ids?: (string | { id: string; label: string })[];
}

function validateCollaborationData(data: unknown): { valid: boolean; data?: CollaborationFormValues; error?: string } {
    if (!data || typeof data !== "object") {
        return { valid: false, error: TEXTS.INVALID_DATA };
    }

    const input = data as Record<string, unknown>;

    if (!input.name || typeof input.name !== "string" || input.name.trim().length === 0) {
        return { valid: false, error: TEXTS.REQUIRED_FIELD_NAME };
    }

    if (!input.api_endpoint || typeof input.api_endpoint !== "string" || input.api_endpoint.trim().length === 0) {
        return { valid: false, error: TEXTS.REQUIRED_FIELD_API_ENDPOINT };
    }

    if (!isValidUrl(input.api_endpoint)) {
        return { valid: false, error: TEXTS.INVALID_URL_FORMAT };
    }

    if (!input.auth_type) {
        return { valid: false, error: TEXTS.INVALID_AUTH_TYPE };
    }

    const validatedData: CollaborationFormValues = {
        name: input.name.trim(),
        description: typeof input.description === "string" ? input.description.trim() : undefined,
        api_endpoint: input.api_endpoint.trim(),
        auth_type: input.auth_type as AuthType,
        auth_value: typeof input.auth_value === "string" ? input.auth_value : undefined,
        dependencies: Array.isArray(input.dependencies) ? (input.dependencies as ConditionDependencyInput[]) : [],
        question_ids: Array.isArray(input.question_ids)
            ? (input.question_ids as (string | { id: string; label: string })[])
            : []
    };

    return { valid: true, data: validatedData };
}

function transformDependenciesToRpcFormat(dependencies: ConditionDependencyInput[]): Json {
    return dependencies.map((dep) => {
        const questionId =
            typeof dep.question_id === "object" && dep.question_id?.id ? dep.question_id.id : dep.question_id;

        return {
            question_id: questionId,
            condition_type: dep.condition_type,
            condition_value: dep.condition_value as Json,
            ...(dep.id && { id: dep.id })
        };
    });
}

function transformQuestionIds(questionIds: (string | { id: string; label: string })[]): string[] {
    return questionIds.map((qId) => {
        if (typeof qId === "object" && qId?.id) {
            return qId.id;
        }
        return qId as string;
    });
}

function prepareAuthValue(authType: AuthType, authValue?: string): string | null {
    return authType !== "none" && authValue ? authValue : null;
}

function prepareCollaborationRpcData(validatedData: CollaborationFormValues) {
    return {
        authValue: prepareAuthValue(validatedData.auth_type, validatedData.auth_value),
        dependencies: transformDependenciesToRpcFormat(validatedData.dependencies || []),
        questionIds: transformQuestionIds(validatedData.question_ids || []),
        baseData: {
            p_name: validatedData.name,
            p_description: validatedData.description || null,
            p_api_endpoint: validatedData.api_endpoint,
            p_auth_type: validatedData.auth_type
        }
    };
}

function revalidateCollaborationsPath() {
    revalidatePath("/admin/collaborations");
}

export async function createCollaboration(data: unknown): Promise<{ success: boolean; error?: string; id?: string }> {
    try {
        const validation = validateCollaborationData(data);
        if (!validation.valid || !validation.data) {
            return { success: false, error: validation.error };
        }

        const { authValue, dependencies, questionIds, baseData } = prepareCollaborationRpcData(validation.data);
        const supabase = await createClientFromRequest();

        const { data: result, error } = await supabase.rpc("create_collaboration_with_dependencies", {
            ...baseData,
            p_auth_value: authValue,
            p_dependencies: dependencies,
            p_question_ids: questionIds
        });

        if (error) throw error;

        const typedResult = result as CollaborationRpcResult;
        if (!typedResult || !typedResult.success) {
            return { success: false, error: typedResult?.error || TEXTS.COLLABORATION_CREATE_ERROR };
        }

        revalidateCollaborationsPath();
        return { success: true, id: typedResult.collaboration_id };
    } catch (error) {
        console.error("Error creating collaboration:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : TEXTS.COLLABORATION_CREATE_ERROR
        };
    }
}

export async function updateCollaboration(id: string, data: unknown): Promise<{ success: boolean; error?: string }> {
    try {
        const validation = validateCollaborationData(data);
        if (!validation.valid || !validation.data) {
            return { success: false, error: validation.error };
        }

        const { authValue, dependencies, questionIds, baseData } = prepareCollaborationRpcData(validation.data);
        const supabase = await createClientFromRequest();

        const { data: result, error } = await supabase.rpc("update_collaboration_with_dependencies", {
            p_collaboration_id: id,
            ...baseData,
            p_auth_value: authValue,
            p_dependencies: dependencies,
            p_question_ids: questionIds
        });

        if (error) throw error;

        const typedResult = result as CollaborationRpcResult;
        if (!typedResult || !typedResult.success) {
            return { success: false, error: typedResult?.error || TEXTS.COLLABORATION_UPDATE_ERROR };
        }

        revalidateCollaborationsPath();
        return { success: true };
    } catch (error) {
        console.error("Error updating collaboration:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : TEXTS.COLLABORATION_UPDATE_ERROR
        };
    }
}

export async function deleteCollaboration(id: string) {
    try {
        const supabase = await createClientFromRequest();

        const { error: conditionLinkError } = await supabase
            .from("link_collaboration_to_condition")
            .delete()
            .eq("collaboration_id", id);

        if (conditionLinkError) throw conditionLinkError;

        const { error: questionLinkError } = await supabase
            .from("link_question_to_collaboration")
            .delete()
            .eq("collaboration_id", id);

        if (questionLinkError) throw questionLinkError;

        const { error: collaborationError } = await supabase.from("collaborations").delete().eq("id", id);

        if (collaborationError) throw collaborationError;

        revalidateCollaborationsPath();
        return { success: true };
    } catch (error) {
        console.error("Error deleting collaboration:", error);
        return {
            success: false,
            error: TEXTS.COLLABORATION_DELETE_ERROR
        };
    }
}

export async function getCollaborationById(
    id: string
): Promise<{ success: boolean; data?: Tables<"collaborations">; error?: string }> {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase.from("collaborations").select("*").eq("id", id).single();

        if (error) throw error;
        if (!data) return { success: false, error: TEXTS.COLLABORATION_NOT_FOUND };

        return { success: true, data };
    } catch (error) {
        console.error("Error fetching collaboration:", error);
        return { success: false, error: TEXTS.COLLABORATION_FETCH_ERROR };
    }
}

export async function getAllCollaborations(): Promise<{
    success: boolean;
    data?: Tables<"collaborations">[];
    error?: string;
}> {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase
            .from("collaborations")
            .select("*")
            .order("created_at", { ascending: false });

        if (error) throw error;

        return { success: true, data: data || [] };
    } catch (error) {
        console.error("Error fetching collaborations:", error);
        return {
            success: false,
            error: TEXTS.COLLABORATION_FETCH_ERROR
        };
    }
}
