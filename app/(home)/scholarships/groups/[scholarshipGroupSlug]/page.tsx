import { notFound } from "next/navigation";

import ScholarshipGroupContent from "@/components/scholarship/scholarship-group-content";
import { createClientFromRequest } from "@/utils/supabase/server";

interface RouteParams {
    scholarshipGroupSlug: string;
}

interface PageProps {
    params: Promise<RouteParams>;
}

export default async function ScholarshipGroupPage({ params }: PageProps) {
    const resolvedParams = await params;
    const supabase = await createClientFromRequest();

    const [groupResult, testimonialsResult] = await Promise.all([
        supabase
            .from("groups_scholarship")
            .select("id, title, description, image_url, slug")
            .eq("slug", resolvedParams.scholarshipGroupSlug)
            .single(),
        supabase
            .from("link_scholarship_groups_to_testimonial")
            .select("testimonial_id")
            .eq(
                "scholarship_group_id",
                (
                    await supabase
                        .from("groups_scholarship")
                        .select("id")
                        .eq("slug", resolvedParams.scholarshipGroupSlug)
                        .single()
                ).data?.id
            )
    ]);

    if (!groupResult.data) {
        return notFound();
    }

    const testimonialIds = testimonialsResult.data?.map((item) => item.testimonial_id) || [];

    return <ScholarshipGroupContent group={groupResult.data} testimonialIds={testimonialIds} />;
}
