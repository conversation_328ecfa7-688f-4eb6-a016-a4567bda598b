import { notFound } from "next/navigation";

import ScholarshipGroupContent from "@/components/scholarship/scholarship-group-content";
import { createClientFromRequest } from "@/utils/supabase/server";

interface RouteParams {
    scholarshipGroupSlug: string;
}

interface PageProps {
    params: Promise<RouteParams>;
}

export default async function ScholarshipGroupPage({ params }: PageProps) {
    const resolvedParams = await params;
    const supabase = await createClientFromRequest();

    // First get the group to extract the ID
    const groupResult = await supabase
        .from("groups_scholarship")
        .select("id, title, description, image_url, slug")
        .eq("slug", resolvedParams.scholarshipGroupSlug)
        .single();

    if (!groupResult.data) {
        return notFound();
    }

    // Now get testimonials using the group ID
    const testimonialsResult = await supabase
        .from("link_scholarship_groups_to_testimonial")
        .select("testimonial_id")
        .eq("scholarship_group_id", groupResult.data.id);



    const testimonialIds = testimonialsResult.data?.map((item) => item.testimonial_id) || [];

    // Transform the data to match component interface (convert null to undefined)
    const groupData = {
        ...groupResult.data,
        image_url: groupResult.data.image_url || undefined
    };

    return <ScholarshipGroupContent group={groupData} testimonialIds={testimonialIds} />;
}
