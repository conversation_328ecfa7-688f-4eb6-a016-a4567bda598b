import { createClientFromRequest } from "@/utils/supabase/server";

type EligibilityResult = {
    data: ScholarshipEligibility[] | null;
    error: string | null;
};

type Condition = {
    id: string;
    question_id: string;
    type: string;
    value?:
        | {
              min?: number;
              max?: number;
              operator?: string;
              days_from_today?: number;
              start_date?: string;
              end_date?: string;
              values?: string[];
          }
        | string[];
};

export type ConditionResult = {
    conditionId: string;
    questionId: string;
    isMet: boolean;
};

export type ScholarshipEligibility = {
    scholarshipId: string;
    title: string;
    slug: string;
    min_amount?: number;
    max_amount?: number;
    isEligible: boolean;
    conditionResults?: ConditionResult[];
    missingAnswers?: string[];
    description?: string;
    short_description?: string;
    start_date?: string;
    end_date?: string;
    url?: string;
    volunteer_hours?: number;
    scholarship_type?: "submission" | "guidance";
    contact_person?: string | null;
    contact_email?: string | null;
    contact_phone?: string | null;
};

export function evaluateCondition(condition: Condition, answer: string | undefined): boolean {
    if (answer === undefined) {
        return false;
    }

    switch (condition.type) {
        case "in": {
            const allowed: string[] = Array.isArray(condition.value)
                ? (condition.value as string[])
                : Array.isArray(condition.value?.values)
                  ? condition.value.values
                  : [];
            let answerId = answer;
            try {
                if (answer && answer.startsWith("{") && answer.endsWith("}")) {
                    const parsed = JSON.parse(answer);
                    if (parsed && typeof parsed === "object" && "id" in parsed) {
                        answerId = parsed.id;
                    }
                }
            } catch (e) {
                console.error("Error parsing answer:", e);
            }
            return allowed.includes(answerId || "");
        }

        case "range": {
            const numAnswer = parseFloat(answer || "0");

            if (!Array.isArray(condition.value)) {
                const min = condition.value?.min !== undefined ? condition.value.min : Number.MIN_SAFE_INTEGER;
                const max = condition.value?.max !== undefined ? condition.value.max : Number.MAX_SAFE_INTEGER;
                return numAnswer >= min && numAnswer <= max;
            }
            return false;
        }

        case "date_range": {
            try {
                if (Array.isArray(condition.value)) {
                    return false;
                }

                const answerDate = new Date(answer || "");
                const today = new Date();

                if (condition.value?.operator === "greater_than") {
                    const daysFromToday = condition.value.days_from_today || 0;
                    const compareDate = new Date();
                    compareDate.setDate(today.getDate() - daysFromToday);
                    return answerDate > compareDate;
                } else if (condition.value?.operator === "less_than") {
                    const daysFromToday = condition.value.days_from_today || 0;
                    const compareDate = new Date();
                    compareDate.setDate(today.getDate() - daysFromToday);
                    return answerDate < compareDate;
                } else if (condition.value?.start_date && condition.value?.end_date) {
                    const startDate = new Date(condition.value.start_date);
                    const endDate = new Date(condition.value.end_date);
                    return answerDate >= startDate && answerDate <= endDate;
                }
            } catch (e) {
                console.error("Error parsing date:", e);
                return false;
            }
            return false;
        }

        default:
            return false;
    }
}

export async function checkScholarshipEligibility(userId: string): Promise<EligibilityResult> {
    const supabase = await createClientFromRequest();

    try {
        const [
            { data: scholarships, error: scholarshipsError },
            { data: userAnswers, error: userAnswersError },
            { data: directConditionLinks, error: directLinksError },
            { data: conditionGroupLinks, error: groupLinksError },
            { data: allConditions, error: conditionsError }
        ] = await Promise.all([
            supabase.from("scholarships").select("*").eq("is_active", true),
            supabase.from("answers").select("question_id, answer").eq("user_id", userId),
            supabase.from("link_scholarship_to_condition").select("scholarship_id, condition_id"),
            supabase.from("link_scholarship_to_condition_groups").select("scholarship_id, group_id"),
            supabase.from("conditions").select("*")
        ]);

        if (scholarshipsError) {
            return {
                data: null,
                error: `Failed to fetch scholarships: ${scholarshipsError.message}`
            };
        }

        if (userAnswersError) {
            return {
                data: null,
                error: `Failed to fetch user answers: ${userAnswersError.message}`
            };
        }

        if (directLinksError) {
            return {
                data: null,
                error: `Failed to fetch direct condition links: ${directLinksError.message}`
            };
        }

        if (groupLinksError) {
            return {
                data: null,
                error: `Failed to fetch condition group links: ${groupLinksError.message}`
            };
        }

        if (conditionsError) {
            return {
                data: null,
                error: `Failed to fetch conditions: ${conditionsError.message}`
            };
        }

        if (!scholarships || scholarships.length === 0) {
            return { data: [], error: null };
        }

        const answerMap = new Map<string, string>();
        userAnswers?.forEach((answer) => {
            answerMap.set(answer.question_id, answer.answer);
        });

        const directConditionsByScholarship = new Map<string, string[]>();
        directConditionLinks?.forEach((link) => {
            if (!directConditionsByScholarship.has(link.scholarship_id)) {
                directConditionsByScholarship.set(link.scholarship_id, []);
            }
            directConditionsByScholarship.get(link.scholarship_id)?.push(link.condition_id);
        });

        const conditionGroupsByScholarship = new Map<string, string[]>();
        conditionGroupLinks?.forEach((link) => {
            if (!conditionGroupsByScholarship.has(link.scholarship_id)) {
                conditionGroupsByScholarship.set(link.scholarship_id, []);
            }
            conditionGroupsByScholarship.get(link.scholarship_id)?.push(link.group_id);
        });

        const conditionsById = new Map<string, Condition>();
        const conditionsByGroup = new Map<string, Condition[]>();

        allConditions?.forEach((condition) => {
            conditionsById.set(condition.id, condition as any);

            if (condition.group_id) {
                if (!conditionsByGroup.has(condition.group_id)) {
                    conditionsByGroup.set(condition.group_id, []);
                }
                conditionsByGroup.get(condition.group_id)?.push(condition as any);
            }
        });

        const eligibilityResults: ScholarshipEligibility[] = scholarships.map((scholarship) => {
            const result: ScholarshipEligibility = {
                scholarshipId: scholarship.id,
                title: scholarship.title,
                slug: scholarship.slug,
                min_amount: scholarship.min_amount,
                max_amount: scholarship.max_amount,
                isEligible: false,
                conditionResults: [],
                missingAnswers: [],
                description: scholarship.description,
                short_description: scholarship.short_description,
                start_date: scholarship.start_date,
                end_date: scholarship.end_date,
                url: scholarship.url || undefined,
                volunteer_hours: scholarship.volunteer_hours,
                scholarship_type: scholarship.scholarship_type,
                contact_person: scholarship.contact_person,
                contact_email: scholarship.contact_email,
                contact_phone: scholarship.contact_phone
            };

            const directConditionIds = directConditionsByScholarship.get(scholarship.id) || [];
            const groupIds = conditionGroupsByScholarship.get(scholarship.id) || [];

            if (directConditionIds.length === 0 && groupIds.length === 0) {
                result.isEligible = true;
                return result;
            }

            let directConditionsMet = true;

            for (const conditionId of directConditionIds) {
                const condition = conditionsById.get(conditionId);

                if (condition) {
                    const answer = answerMap.get(condition.question_id);
                    const isMet = evaluateCondition(condition, answer);

                    result.conditionResults?.push({
                        conditionId: condition.id,
                        questionId: condition.question_id,
                        isMet
                    });

                    if (!answer) {
                        result.missingAnswers?.push(condition.question_id);
                    }

                    if (!isMet) {
                        directConditionsMet = false;
                    }
                }
            }

            let anyGroupFullyMet = groupIds.length === 0;

            for (const groupId of groupIds) {
                const groupConditions = conditionsByGroup.get(groupId) || [];

                if (groupConditions.length === 0) {
                    anyGroupFullyMet = true;
                    continue;
                }

                let allConditionsInGroupMet = true;

                for (const condition of groupConditions) {
                    const answer = answerMap.get(condition.question_id);
                    const isMet = evaluateCondition(condition, answer);

                    result.conditionResults?.push({
                        conditionId: condition.id,
                        questionId: condition.question_id,
                        isMet
                    });

                    if (!answer) {
                        result.missingAnswers?.push(condition.question_id);
                    }

                    if (!isMet) {
                        allConditionsInGroupMet = false;
                    }
                }

                if (allConditionsInGroupMet) {
                    anyGroupFullyMet = true;
                }
            }

            if (directConditionIds.length > 0) {
                if (groupIds.length > 0) {
                    result.isEligible = directConditionsMet && anyGroupFullyMet;
                } else {
                    result.isEligible = directConditionsMet;
                }
            } else {
                result.isEligible = anyGroupFullyMet;
            }

            if (result.missingAnswers && result.missingAnswers.length > 0) {
                result.missingAnswers = Array.from(new Set(result.missingAnswers));
            }

            return result;
        });

        return { data: eligibilityResults, error: null };
    } catch (error) {
        console.error("Error in checkScholarshipEligibility:", error);
        return {
            data: null,
            error: error instanceof Error ? error.message : "Unknown error occurred"
        };
    }
}
