-- Fix RLS policies for document_examples bucket to work with Clerk authentication
-- This resolves the "new row violates row-level security policy" error when uploading files

-- Drop existing policies if they exist to avoid conflicts
DROP POLICY IF EXISTS public_upload_document_examples ON storage.objects;
DROP POLICY IF EXISTS public_update_document_examples ON storage.objects;
DROP POLICY IF EXISTS anon_upload_document_examples ON storage.objects;
DROP POLICY IF EXISTS anon_update_document_examples ON storage.objects;

-- Allow public users to insert files into document_examples bucket
CREATE POLICY public_upload_document_examples 
ON storage.objects 
FOR INSERT 
TO public 
WITH CHECK (bucket_id = 'document_examples');

-- Allow public users to update files in document_examples bucket  
CREATE POLICY public_update_document_examples 
ON storage.objects 
FOR UPDATE 
TO public 
USING (bucket_id = 'document_examples') 
WITH CHECK (bucket_id = 'document_examples');

-- Allow anonymous users to insert files into document_examples bucket
-- This is needed because Supabase client with anon key connects with 'anon' role
CREATE POLICY anon_upload_document_examples 
ON storage.objects 
FOR INSERT 
TO anon 
WITH CHECK (bucket_id = 'document_examples');

-- Allow anonymous users to update files in document_examples bucket  
CREATE POLICY anon_update_document_examples 
ON storage.objects 
FOR UPDATE 
TO anon 
USING (bucket_id = 'document_examples') 
WITH CHECK (bucket_id = 'document_examples');

-- Comments removed to avoid permission errors in local development
-- These policies allow:
-- - public/anon users to upload/update files to document_examples bucket 