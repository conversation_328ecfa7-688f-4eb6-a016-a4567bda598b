-- Fix RLS policies for groups_scholarship bucket to work with Clerk authentication
-- This resolves the "new row violates row-level security policy" error when uploading files

-- Drop existing policies if they exist to avoid conflicts
DROP POLICY IF EXISTS "Public Access" ON storage.objects;
DROP POLICY IF EXISTS "Employees and admins can upload files" ON storage.objects;
DROP POLICY IF EXISTS "Employees and admins can update files" ON storage.objects;
DROP POLICY IF EXISTS "Employees and admins can delete files" ON storage.objects;
DROP POLICY IF EXISTS public_upload_groups_scholarship ON storage.objects;
DROP POLICY IF EXISTS public_update_groups_scholarship ON storage.objects;
DROP POLICY IF EXISTS public_read_groups_scholarship ON storage.objects;
DROP POLICY IF EXISTS anon_upload_groups_scholarship ON storage.objects;
DROP POLICY IF EXISTS anon_update_groups_scholarship ON storage.objects;
DROP POLICY IF EXISTS authenticated_delete_groups_scholarship ON storage.objects;

-- Allow public users to insert files into groups_scholarship bucket
CREATE POLICY public_upload_groups_scholarship 
ON storage.objects 
FOR INSERT 
TO public 
WITH CHECK (bucket_id = 'groups_scholarship');

-- Allow public users to update files in groups_scholarship bucket  
CREATE POLICY public_update_groups_scholarship 
ON storage.objects 
FOR UPDATE 
TO public 
USING (bucket_id = 'groups_scholarship') 
WITH CHECK (bucket_id = 'groups_scholarship');

-- Allow anonymous users to insert files into groups_scholarship bucket
-- This is needed because Supabase client with anon key connects with 'anon' role
CREATE POLICY anon_upload_groups_scholarship 
ON storage.objects 
FOR INSERT 
TO anon 
WITH CHECK (bucket_id = 'groups_scholarship');

-- Allow anonymous users to update files in groups_scholarship bucket  
CREATE POLICY anon_update_groups_scholarship 
ON storage.objects 
FOR UPDATE 
TO anon 
USING (bucket_id = 'groups_scholarship') 
WITH CHECK (bucket_id = 'groups_scholarship');

-- Keep the existing read and delete policies but update them for the new naming convention
-- Allow public read access (this was already working)
CREATE POLICY public_read_groups_scholarship
ON storage.objects FOR SELECT
TO public
USING (bucket_id = 'groups_scholarship');

-- Allow authenticated users to delete files (maintain existing security level)
CREATE POLICY authenticated_delete_groups_scholarship
ON storage.objects FOR DELETE
TO authenticated
USING (bucket_id = 'groups_scholarship');

-- Comments removed to avoid permission errors in local development
-- These policies allow:
-- - public/anon users to upload/update files to groups_scholarship bucket  
-- - public read access to scholarship group images
-- - authenticated users to delete files from groups_scholarship bucket 