-- Create user_documents bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'user_documents',
    'user_documents',
    false, -- User files are typically private
    10485760, -- 10MB, adjust as needed
    ARRAY[
        'application/pdf', 
        'image/jpeg', 
        'image/png', 
        'image/webp', 
        'text/plain',
        'application/msword', 
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation'
    ]
)
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Policies for user_documents bucket
-- These policies assume that documents are stored with the user's ID (UUID) as the first segment of the file path.
-- For example: '{user_id}/documents/report.pdf'

-- Policies for user_documents bucket are being removed.
-- Access control will be handled by server actions using Clerk authentication.
-- Server actions should use a Supabase client with service_role privileges for storage operations.

-- Drop any existing policies on storage.objects for the user_documents bucket to ensure a clean state.
-- Note: Policy names might have varied through iterations. Dropping known names.
DROP POLICY IF EXISTS "Users can view their own files" ON storage.objects;
DROP POLICY IF EXISTS "Users can view their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload to their own folder" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload documents to their own folder" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own files" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own files" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can select documents" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can insert documents" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can update documents" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete documents" ON storage.objects;

-- No new policies are created, effectively disabling RLS for this bucket if no other policies apply.
-- Or, ensure a default public-but-no-access policy isn't present, or that service role bypasses it.
-- For true RLS removal for this bucket, one might also disable RLS on the bucket itself if Supabase UI allows,
-- but typically, having NO policies means access is denied unless by a superuser/service_role.
-- The goal here is to let service_role (used by server actions) operate freely.
