-- Create atomic RPC functions for collaboration operations
-- These functions handle all collaboration-related operations in a single transaction

-- Create RPC function for atomic collaboration creation
CREATE OR REPLACE FUNCTION create_collaboration_with_dependencies(
    p_name TEXT,
    p_description TEXT,
    p_api_endpoint TEXT,
    p_auth_type public.auth_type,
    p_auth_value TEXT,
    p_dependencies JSONB DEFAULT '[]'::JSONB,
    p_question_ids TEXT[] DEFAULT ARRAY[]::TEXT[]
) RETURNS JSONB AS $$
DECLARE
    v_collaboration_id UUID;
    v_condition_id UUID;
    v_dependency JSONB;
    v_condition_type public.condition_type;
    v_condition_value JSONB;
    v_question_id TEXT;
    v_question_id_str TEXT;
    v_result JSONB := '{"success": true}'::JSONB;
    v_condition_ids UUID[] := ARRAY[]::UUID[];
BEGIN
    -- Validate required fields
    IF p_name IS NULL OR TRIM(p_name) = '' THEN
        RAISE EXCEPTION 'Name is required';
    END IF;
    
    IF p_api_endpoint IS NULL OR TRIM(p_api_endpoint) = '' THEN
        RAISE EXCEPTION 'API endpoint is required';
    END IF;
    
    -- Create collaboration record
    INSERT INTO collaborations (
        name,
        description,
        api_endpoint,
        auth_type,
        auth_value
    ) VALUES (
        TRIM(p_name),
        CASE WHEN p_description IS NOT NULL AND TRIM(p_description) != '' THEN TRIM(p_description) ELSE NULL END,
        TRIM(p_api_endpoint),
        p_auth_type,
        CASE WHEN p_auth_type != 'none' AND p_auth_value IS NOT NULL THEN p_auth_value ELSE NULL END
    ) RETURNING id INTO v_collaboration_id;
    
    -- Process dependencies (conditions)
    FOR v_dependency IN SELECT * FROM jsonb_array_elements(p_dependencies)
    LOOP
        -- Validate required fields
        IF NOT (v_dependency ? 'question_id') OR (v_dependency->>'question_id') IS NULL THEN
            RAISE EXCEPTION 'Missing question_id in dependency: %', v_dependency;
        END IF;
        
        IF NOT (v_dependency ? 'condition_type') OR (v_dependency->>'condition_type') IS NULL THEN
            RAISE EXCEPTION 'Missing condition_type in dependency: %', v_dependency;
        END IF;
        
        -- Extract question_id, handling both string and object formats
        IF jsonb_typeof(v_dependency->'question_id') = 'object' THEN
            v_question_id_str := v_dependency->'question_id'->>'id';
        ELSE
            v_question_id_str := v_dependency->>'question_id';
        END IF;
        
        -- Validate that the question exists
        IF NOT EXISTS (SELECT 1 FROM questions WHERE id = v_question_id_str::UUID) THEN
            RAISE EXCEPTION 'Question with id % does not exist', v_question_id_str;
        END IF;
        
        -- Set condition type and value
        v_condition_type := (v_dependency->>'condition_type')::public.condition_type;
        v_condition_value := v_dependency->'condition_value';
        
        -- Create condition
        INSERT INTO conditions (
            question_id,
            type,
            value,
            group_id
        ) VALUES (
            v_question_id_str::UUID,
            v_condition_type,
            v_condition_value,
            NULL
        ) RETURNING id INTO v_condition_id;
        
        -- Link condition to collaboration
        INSERT INTO link_collaboration_to_condition (
            collaboration_id,
            condition_id
        ) VALUES (
            v_collaboration_id,
            v_condition_id
        );
        
        -- Add condition ID to result array
        v_condition_ids := v_condition_ids || v_condition_id;
    END LOOP;
    
    -- Process question links
    FOREACH v_question_id IN ARRAY p_question_ids
    LOOP
        -- Validate that the question exists
        IF NOT EXISTS (SELECT 1 FROM questions WHERE id = v_question_id::UUID) THEN
            RAISE EXCEPTION 'Question with id % does not exist', v_question_id;
        END IF;
        
        -- Create question-collaboration link
        INSERT INTO link_question_to_collaboration (
            collaboration_id,
            question_id  
        ) VALUES (
            v_collaboration_id,
            v_question_id::UUID
        );
    END LOOP;
    
    -- Build result
    v_result := jsonb_set(v_result, '{collaboration_id}', to_jsonb(v_collaboration_id));
    v_result := jsonb_set(v_result, '{condition_ids}', to_jsonb(v_condition_ids));
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        -- Any error will automatically rollback the transaction
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'error_code', SQLSTATE
        );
END;
$$ LANGUAGE plpgsql
SET search_path = '';

-- Create RPC function for atomic collaboration update
CREATE OR REPLACE FUNCTION update_collaboration_with_dependencies(
    p_collaboration_id UUID,
    p_name TEXT,
    p_description TEXT,
    p_api_endpoint TEXT,
    p_auth_type public.auth_type,
    p_auth_value TEXT,
    p_dependencies JSONB DEFAULT '[]'::JSONB,
    p_question_ids TEXT[] DEFAULT ARRAY[]::TEXT[]
) RETURNS JSONB AS $$
DECLARE
    v_condition_id UUID;
    v_dependency JSONB;
    v_condition_type public.condition_type;
    v_condition_value JSONB;
    v_question_id TEXT;
    v_question_id_str TEXT;
    v_result JSONB := '{"success": true}'::JSONB;
    v_condition_ids UUID[] := ARRAY[]::UUID[];
    v_existing_condition_ids UUID[];
    v_updated_condition_ids UUID[] := ARRAY[]::UUID[];
BEGIN
    -- Validate collaboration exists
    IF NOT EXISTS (SELECT 1 FROM collaborations WHERE id = p_collaboration_id) THEN
        RAISE EXCEPTION 'Collaboration with id % does not exist', p_collaboration_id;
    END IF;
    
    -- Validate required fields
    IF p_name IS NULL OR TRIM(p_name) = '' THEN
        RAISE EXCEPTION 'Name is required';
    END IF;
    
    IF p_api_endpoint IS NULL OR TRIM(p_api_endpoint) = '' THEN
        RAISE EXCEPTION 'API endpoint is required';
    END IF;
    
    -- Update collaboration record
    UPDATE collaborations SET
        name = TRIM(p_name),
        description = CASE WHEN p_description IS NOT NULL AND TRIM(p_description) != '' THEN TRIM(p_description) ELSE NULL END,
        api_endpoint = TRIM(p_api_endpoint),
        auth_type = p_auth_type,
        auth_value = CASE WHEN p_auth_type != 'none' AND p_auth_value IS NOT NULL THEN p_auth_value ELSE NULL END,
        updated_at = NOW()
    WHERE id = p_collaboration_id;
    
    -- Get existing condition IDs linked to this collaboration
    SELECT ARRAY_AGG(condition_id) INTO v_existing_condition_ids
    FROM link_collaboration_to_condition
    WHERE collaboration_id = p_collaboration_id;
    
    IF v_existing_condition_ids IS NULL THEN
        v_existing_condition_ids := ARRAY[]::UUID[];
    END IF;
    
    -- Process new/updated dependencies
    FOR v_dependency IN SELECT * FROM jsonb_array_elements(p_dependencies)
    LOOP
        -- Validate required fields
        IF NOT (v_dependency ? 'question_id') OR (v_dependency->>'question_id') IS NULL THEN
            RAISE EXCEPTION 'Missing question_id in dependency: %', v_dependency;
        END IF;
        
        IF NOT (v_dependency ? 'condition_type') OR (v_dependency->>'condition_type') IS NULL THEN
            RAISE EXCEPTION 'Missing condition_type in dependency: %', v_dependency;
        END IF;
        
        -- Extract question_id, handling both string and object formats
        IF jsonb_typeof(v_dependency->'question_id') = 'object' THEN
            v_question_id_str := v_dependency->'question_id'->>'id';
        ELSE
            v_question_id_str := v_dependency->>'question_id';
        END IF;
        
        -- Validate that the question exists
        IF NOT EXISTS (SELECT 1 FROM questions WHERE id = v_question_id_str::UUID) THEN
            RAISE EXCEPTION 'Question with id % does not exist', v_question_id_str;
        END IF;
        
        -- Set condition type and value
        v_condition_type := (v_dependency->>'condition_type')::public.condition_type;
        v_condition_value := v_dependency->'condition_value';
        
        -- Check if this is an existing condition (has id and id exists in existing conditions)
        IF (v_dependency ? 'id') AND (v_dependency->>'id')::UUID = ANY(v_existing_condition_ids) THEN
            -- Update existing condition
            v_condition_id := (v_dependency->>'id')::UUID;
            
            UPDATE conditions SET
                question_id = v_question_id_str::UUID,
                type = v_condition_type,
                value = v_condition_value,
                updated_at = NOW()
            WHERE id = v_condition_id;
            
            -- Track updated condition
            v_updated_condition_ids := v_updated_condition_ids || v_condition_id;
        ELSE
            -- Create new condition
            INSERT INTO conditions (
                question_id,
                type,
                value,
                group_id
            ) VALUES (
                v_question_id_str::UUID,
                v_condition_type,
                v_condition_value,
                NULL
            ) RETURNING id INTO v_condition_id;
            
            -- Link new condition to collaboration
            INSERT INTO link_collaboration_to_condition (
                collaboration_id,
                condition_id
            ) VALUES (
                p_collaboration_id,
                v_condition_id
            );
        END IF;
        
        -- Add condition ID to result array
        v_condition_ids := v_condition_ids || v_condition_id;
    END LOOP;
    
    -- Delete unused existing conditions and their links
    IF array_length(v_existing_condition_ids, 1) > 0 THEN
        -- Find conditions to delete (existing conditions not in updated list)
        DELETE FROM link_collaboration_to_condition
        WHERE collaboration_id = p_collaboration_id 
        AND condition_id = ANY(
            SELECT unnest(v_existing_condition_ids) 
            EXCEPT 
            SELECT unnest(v_updated_condition_ids)
        );
        
        -- Delete the orphaned conditions
        DELETE FROM conditions
        WHERE id = ANY(
            SELECT unnest(v_existing_condition_ids) 
            EXCEPT 
            SELECT unnest(v_updated_condition_ids)
        );
    END IF;
    
    -- Delete existing question links
    DELETE FROM link_question_to_collaboration
    WHERE collaboration_id = p_collaboration_id;
    
    -- Process new question links
    FOREACH v_question_id IN ARRAY p_question_ids
    LOOP
        -- Validate that the question exists
        IF NOT EXISTS (SELECT 1 FROM questions WHERE id = v_question_id::UUID) THEN
            RAISE EXCEPTION 'Question with id % does not exist', v_question_id;
        END IF;
        
        -- Create question-collaboration link
        INSERT INTO link_question_to_collaboration (
            collaboration_id,
            question_id  
        ) VALUES (
            p_collaboration_id,
            v_question_id::UUID
        );
    END LOOP;
    
    -- Build result
    v_result := jsonb_set(v_result, '{collaboration_id}', to_jsonb(p_collaboration_id));
    v_result := jsonb_set(v_result, '{condition_ids}', to_jsonb(v_condition_ids));
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        -- Any error will automatically rollback the transaction
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'error_code', SQLSTATE
        );
END;
$$ LANGUAGE plpgsql
SET search_path = '';

-- Add comments for documentation
COMMENT ON FUNCTION create_collaboration_with_dependencies(TEXT, TEXT, TEXT, public.auth_type, TEXT, JSONB, TEXT[]) IS 'Atomically creates collaboration with its conditions and question links';
COMMENT ON FUNCTION update_collaboration_with_dependencies(UUID, TEXT, TEXT, TEXT, public.auth_type, TEXT, JSONB, TEXT[]) IS 'Atomically updates collaboration with its conditions and question links'; 