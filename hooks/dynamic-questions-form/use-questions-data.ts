import type { UserResource } from "@clerk/types";
import type React from "react";
import { useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { fetchQuestionsData } from "@/app/actions/dynamic-questions-form-actions";

import type { DynamicQuestionsFormProps } from "../../components/forms/dynamic-questions-form/dynamic-questions-form";
import type {
    Answer,
    Condition,
    FormValues,
    Question,
    QuestionConditionLink,
    QuestionSection
} from "../../components/forms/dynamic-questions-form/types";
import { createDefaultFormValues, processAnswers } from "@/components/forms/dynamic-questions-form/utils";
import type { QuestionsAction } from "./use-dynamic-questions-state";
import { useUserOrgRole } from "@/hooks/use-user-org-role";

interface UseQuestionsDataParams {
    sections: QuestionSection[];
    scholarshipId?: string;
    isLoaded: boolean;
    user: UserResource | null;
    overrideUserId?: string;
    prefetchedData?: DynamicQuestionsFormProps["prefetchedData"];
    dispatch: (action: QuestionsAction) => void;
    lastFetchedSections: string[];
    lastFetchedScholarshipId?: string;
    isSubmittingRef: React.MutableRefObject<boolean>;
}

export function useQuestionsData({
    sections,
    scholarshipId,
    isLoaded,
    user,
    overrideUserId,
    prefetchedData,
    dispatch,
    lastFetchedSections,
    lastFetchedScholarshipId,
    isSubmittingRef
}: UseQuestionsDataParams) {
    const methods = useForm<FormValues>();

    useEffect(() => {
        if (!prefetchedData) return;

        if (isSubmittingRef.current) {
            return;
        }

        const { questions: rawQuestions, questionGroups, conditions, questionConditionLinks, answers } = prefetchedData;

        if (!rawQuestions || rawQuestions.length === 0) {
            dispatch({
                type: "SET_DATA",
                payload: {
                    questions: [],
                    questionGroups: {},
                    conditions: [],
                    questionConditionLinks: [],
                    sections: sections as string[],
                    scholarshipId
                }
            });
            methods.reset({});
            dispatch({ type: "SET_LOADING", payload: false });
            return;
        }

        const typedQuestions = rawQuestions as Question[];

        const filteredQuestions = typedQuestions.filter((question) =>
            sections.includes(question.section as QuestionSection)
        );

        const defaultValues = createDefaultFormValues(filteredQuestions);
        const answersMap = processAnswers(answers as Answer[], filteredQuestions);
        const finalInitialValues = { ...defaultValues, ...answersMap };

        dispatch({
            type: "SET_DATA",
            payload: {
                questions: filteredQuestions,
                questionGroups,
                conditions: conditions as Condition[],
                questionConditionLinks: questionConditionLinks as QuestionConditionLink[],
                sections: sections as string[],
                scholarshipId
            }
        });

        if (!isSubmittingRef.current && !methods.formState.isSubmitting) {
            methods.reset(finalInitialValues);
        }
        dispatch({ type: "SET_LOADING", payload: false });
    }, [prefetchedData, dispatch, sections, scholarshipId, isSubmittingRef, methods]);

    const effectiveUserId = overrideUserId || user?.id;
    const { role: userOrgRole, isLoaded: isOrgRoleLoaded } = useUserOrgRole();

    const shouldFetchFreshData = useMemo(() => {
        if (prefetchedData) return false;
        if (!effectiveUserId || (!overrideUserId && (!isLoaded || !user))) return false;
        if (sections.length === 0) return false;

        return !(
            lastFetchedSections.length === sections.length &&
            lastFetchedSections.every((s) => sections.includes(s as QuestionSection)) &&
            lastFetchedScholarshipId === scholarshipId
        );
    }, [
        prefetchedData,
        isLoaded,
        user,
        overrideUserId,
        effectiveUserId,
        sections,
        lastFetchedSections,
        lastFetchedScholarshipId,
        scholarshipId
    ]);

    useEffect(() => {
        if (!shouldFetchFreshData) return;

        let isMounted = true;

        const fetchData = async () => {
            if (!effectiveUserId) {
                dispatch({ type: "SET_ERROR", payload: "User not authenticated." });
                return;
            }

            if (
                overrideUserId &&
                user?.id &&
                overrideUserId !== user.id &&
                isOrgRoleLoaded &&
                userOrgRole !== "admin" &&
                userOrgRole !== "employee"
            ) {
                dispatch({ type: "SET_ERROR", payload: "נדרשות הרשאות מנהל לביצוע פעולה זו." });
                toast.error("נדרשות הרשאות מנהל לביצוע פעולה זו.");
                return;
            }

            if (isSubmittingRef.current) {
                return;
            }

            dispatch({ type: "SET_LOADING", payload: true });

            try {
                const result = await fetchQuestionsData(sections as string[], effectiveUserId, scholarshipId);

                if (!isMounted) return;

                if (!result.success || !result.data) {
                    dispatch({ type: "SET_ERROR", payload: result.error || "שגיאה בטעינת השאלות" });
                    toast.error("שגיאה בטעינת השאלות");
                    dispatch({ type: "SET_LOADING", payload: false });
                    return;
                }

                const {
                    questions: rawQuestions,
                    questionGroups,
                    conditions,
                    questionConditionLinks,
                    answers
                } = result.data;

                if (!rawQuestions || rawQuestions.length === 0) {
                    dispatch({
                        type: "SET_DATA",
                        payload: {
                            questions: [],
                            questionGroups: {},
                            conditions: [],
                            questionConditionLinks: [],
                            sections: sections as string[],
                            scholarshipId
                        }
                    });

                    if (!isSubmittingRef.current && !methods.formState.isSubmitting) {
                        methods.reset({});
                    }
                    dispatch({ type: "SET_LOADING", payload: false });
                    return;
                }

                const typedQuestions = rawQuestions as Question[];

                const filteredQuestions = typedQuestions.filter((question) =>
                    sections.includes(question.section as QuestionSection)
                );

                const defaultValues = createDefaultFormValues(filteredQuestions);
                const answersMap = processAnswers(answers as Answer[], filteredQuestions);
                const finalInitialValues = { ...defaultValues, ...answersMap };

                dispatch({
                    type: "SET_DATA",
                    payload: {
                        questions: filteredQuestions,
                        questionGroups,
                        conditions: conditions as Condition[],
                        questionConditionLinks: questionConditionLinks as QuestionConditionLink[],
                        sections: sections as string[],
                        scholarshipId
                    }
                });

                if (!isSubmittingRef.current && !methods.formState.isSubmitting) {
                    methods.reset(finalInitialValues);
                }
                dispatch({ type: "SET_LOADING", payload: false });
            } catch {
                if (!isMounted) return;
                dispatch({ type: "SET_ERROR", payload: "שגיאה בטעינת השאלות" });
                toast.error("שגיאה בטעינת השאלות");
                dispatch({ type: "SET_LOADING", payload: false });
            }
        };

        fetchData();

        return () => {
            isMounted = false;
        };
    }, [shouldFetchFreshData, effectiveUserId, sections, scholarshipId, dispatch, isSubmittingRef, methods]);

    return methods;
}
