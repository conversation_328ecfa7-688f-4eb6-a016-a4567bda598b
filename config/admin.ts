import {
    BellIcon,
    CircleDollarSignIcon,
    CircleHelpIcon,
    ClipboardListIcon,
    FileTextIcon,
    FilesIcon,
    GraduationCapIcon,
    HandshakeIcon,
    LandmarkIcon,
    LayoutDashboardIcon,
    CrownIcon,
    MailIcon,
    MessageCircleIcon,
    OctagonAlertIcon,
    UserIcon
} from "lucide-react";

export const sidebarData = {
    navMain: [
        {
            title: "אדמין",
            url: "/admin",
            icon: CrownIcon
        },
        {
            title: "הכרזות",
            url: "/admin/banners",
            icon: BellIcon
        }
    ],
    generalManagement: {
        title: "כספים",
        icon: LandmarkIcon,
        items: [
            {
                name: "קופונים",
                url: "/admin/coupons",
                icon: CircleDollarSignIcon
            },
            {
                name: "שיתופי פעולה",
                url: "/admin/collaborations",
                icon: HandshakeIcon
            },
            {
                name: "משתמשים",
                url: "/admin/users",
                icon: UserIcon
            }
        ]
    },
    scholarshipManagement: {
        title: "ניהול מלגות",
        icon: GraduationCapIcon,
        items: [
            {
                name: "מלגות",
                url: "/admin/scholarships",
                icon: GraduationCapIcon
            },
            {
                name: "שאלות",
                url: "/admin/questions",
                icon: CircleHelpIcon
            },
            {
                name: "קבוצות תנאים",
                url: "/admin/scholarships/conditions/groups",
                icon: OctagonAlertIcon
            },
            {
                name: "סוגי מסמכים",
                url: "/admin/document-types",
                icon: FilesIcon
            }
        ]
    },
    contentManagement: {
        title: "ניהול תוכן אתר",
        icon: FileTextIcon,
        items: [
            {
                name: "טופס יצירת קשר",
                url: "/admin/contact",
                icon: MailIcon
            },
            {
                name: "שאלות ותשובות",
                url: "/admin/faq",
                icon: MessageCircleIcon
            },
            {
                name: "חוות דעת",
                url: "/admin/testimonials",
                icon: ClipboardListIcon
            }
        ]
    },
    navSecondary: [
        {
            title: "דשבורד",
            url: "/dashboard",
            icon: LayoutDashboardIcon
        }
    ]
};
