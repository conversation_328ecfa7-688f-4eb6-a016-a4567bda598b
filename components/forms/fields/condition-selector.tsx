"use client";

import { Plus<PERSON>ircle, X } from "lucide-react";
import React, { useEffect } from "react";
import { Controller, useFormContext } from "react-hook-form";

import { Option } from "@/components/forms/fields/dropdown-base";
import { MultiSelect } from "@/components/forms/fields/multi-select";
import { NumberInput } from "@/components/forms/fields/number-input";
import { SingleSelect } from "@/components/forms/fields/single-select";
import { FieldType } from "@/components/forms/fields/types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { mapIdsToOptions } from "@/utils/form-utils";
import { textToArray } from "@/utils/formatters";

export type ConditionQuestion = {
    id: string;
    type: FieldType;
    metadata?: {
        label?: string;
        placeholder?: string;
        options?: string | Array<{ value: string; label: string }> | Array<{ id: string; label: string }>;
    };
    groups_question?: {
        id?: string;
        name?: string;
    };
};

export type ConditionType = "range" | "date_range" | "in";

export interface RangeConditionValue {
    min?: number;
    max?: number;
}

export interface DateRangeConditionValue {
    operator: "greater_than" | "less_than";
    days_from_today: number;
}

export type InConditionValue = Array<Option>;

export type ConditionValue = RangeConditionValue | DateRangeConditionValue | InConditionValue;

export type ConditionDependency = {
    question_id: Option;
    condition_type: ConditionType;
    condition_value: ConditionValue;
    id?: string;
};

export interface ConditionSelectorProps {
    availableQuestions: ConditionQuestion[];
    onAdd: () => void;
    onRemove: (index: number) => void;
    fieldArrayName?: string;
}

const TEXTS = {
    numberMinLabel: "גדול מ",
    numberMinPlaceholder: "הכנס ערך מינימלי",
    numberMaxLabel: "קטן מ",
    numberMaxPlaceholder: "הכנס ערך מקסימלי",
    dateTitle: "תנאי על הפרש תאריכים",
    dateOperatorGreaterThan: "גדול מ",
    dateOperatorLessThan: "קטן מ",
    dateHelp: "ההפרש בימים בין התאריך שנבחר לבין היום",
    dateConditionPlaceholder: "בחר תנאי",
    dateDaysPlaceholder: "מספר ימים",
    selectValuesLabel: "ערכים נבחרים",
    selectValuesPlaceholder: "בחר ערכים",
    selectQuestionLabel: "בחר שאלה",
    selectQuestionPlaceholder: "בחר שאלה",
    addDependencyButton: "הוסף תלות",

    errorLabel: "שגיאה:",
    questionNotFoundError: "השאלה הנבחרת לא נמצאה במערכת",
    questionNotFoundHelp: "יש לבחור שאלה חדשה או להסיר את התלות הזו"
};

const renderSpecificConditionFields = (
    dependency: ConditionDependency,
    index: number,
    availableQuestions: ConditionQuestion[],
    methods: ReturnType<typeof useFormContext>,
    fieldArrayName: string
) => {
    const selectedQuestion = availableQuestions.find((q) => q.id === dependency.question_id.id);
    if (!selectedQuestion) {
        return (
            <div className="rounded-md border border-destructive bg-destructive/10 p-3">
                <div className="flex items-center gap-2 text-sm text-destructive">
                    <span className="font-medium">{TEXTS.errorLabel}</span>
                    <span>
                        {TEXTS.questionNotFoundError} (ID: {dependency.question_id.id})
                    </span>
                </div>
                <div className="mt-1 text-xs text-muted-foreground">{TEXTS.questionNotFoundHelp}</div>
            </div>
        );
    }

    switch (selectedQuestion.type) {
        case "number_input":
            return (
                <div className="flex items-start gap-4">
                    <div className="flex-1">
                        <NumberInput
                            name={`${fieldArrayName}.${index}.condition_value.min`}
                            label={TEXTS.numberMinLabel}
                            placeholder={TEXTS.numberMinPlaceholder}
                        />
                    </div>
                    <div className="flex-1">
                        <NumberInput
                            name={`${fieldArrayName}.${index}.condition_value.max`}
                            label={TEXTS.numberMaxLabel}
                            placeholder={TEXTS.numberMaxPlaceholder}
                        />
                    </div>
                </div>
            );
        case "date_picker":
            return (
                <div className="space-y-2">
                    <Label>{TEXTS.dateTitle}</Label>
                    <div className="flex gap-2">
                        <div className="flex-1">
                            <Controller
                                name={`${fieldArrayName}.${index}.condition_value.operator`}
                                control={methods.control}
                                defaultValue="greater_than"
                                render={({ field }) => (
                                    <Select dir="rtl" onValueChange={field.onChange} value={field.value}>
                                        <SelectTrigger>
                                            <SelectValue placeholder={TEXTS.dateConditionPlaceholder} />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="greater_than">
                                                {TEXTS.dateOperatorGreaterThan}
                                            </SelectItem>
                                            <SelectItem value="less_than">{TEXTS.dateOperatorLessThan}</SelectItem>
                                        </SelectContent>
                                    </Select>
                                )}
                            />
                        </div>
                        <div className="flex-1">
                            <Input
                                {...methods.register(`${fieldArrayName}.${index}.condition_value.days_from_today`, {
                                    valueAsNumber: true
                                })}
                                className="placeholder:text-right"
                                dir="rtl"
                                type="number"
                                placeholder={TEXTS.dateDaysPlaceholder}
                            />
                        </div>
                    </div>
                    <div className="text-sm text-muted-foreground">{TEXTS.dateHelp}</div>
                </div>
            );
        case "single_select":
        case "multi_select": {
            const options = selectedQuestion.metadata?.options;
            if (!options) return null;

            let optionsArray: Option[] = [];
            if (typeof options === "string") {
                const stringOptions = textToArray(options);
                optionsArray = mapIdsToOptions(
                    stringOptions,
                    stringOptions,
                    (opt) => opt,
                    (opt) => opt
                );
            } else if (Array.isArray(options)) {
                const processedOptions = options.map((opt) => {
                    if (typeof opt === "string") {
                        return { id: opt, label: opt };
                    } else if (typeof opt === "object" && "id" in opt && "label" in opt) {
                        return { id: opt.id, label: opt.label };
                    } else {
                        const value = opt.value || opt.toString();
                        return {
                            id: value,
                            label: opt.label || value
                        };
                    }
                });

                optionsArray = mapIdsToOptions(
                    processedOptions.map((opt) => opt.id),
                    processedOptions,
                    (opt) => opt.id,
                    (opt) => opt.label
                );
            }

            return (
                <MultiSelect
                    name={`${fieldArrayName}.${index}.condition_value`}
                    label={TEXTS.selectValuesLabel}
                    placeholder={TEXTS.selectValuesPlaceholder}
                    options={optionsArray}
                    showSearch={true}
                />
            );
        }
        default:
            return null;
    }
};

export const ConditionSelector: React.FC<ConditionSelectorProps> = ({
    availableQuestions,
    onAdd,
    onRemove,
    fieldArrayName = "dependencies"
}) => {
    const methods = useFormContext();
    const watchedDependencies = methods.watch(fieldArrayName);

    const EXCLUDED_DEPENDENCY_TYPES: FieldType[] = ["short_text", "long_text", "address_select", "bank_select"];
    const filteredAvailableQuestions = availableQuestions.filter((q) => !EXCLUDED_DEPENDENCY_TYPES.includes(q.type));

    useEffect(() => {
        if (!watchedDependencies) return;

        watchedDependencies.forEach((dependency: ConditionDependency, index: number) => {
            const questionId = dependency.question_id?.id;
            if (questionId && !dependency.condition_type) {
                const selectedQuestion = availableQuestions.find((q) => q.id === questionId);
                if (!selectedQuestion) return;

                let defaultConditionType: ConditionType;
                switch (selectedQuestion.type) {
                    case "number_input":
                        defaultConditionType = "range";
                        methods.setValue(`${fieldArrayName}.${index}.condition_type`, defaultConditionType);
                        methods.setValue(`${fieldArrayName}.${index}.condition_value`, {
                            min: undefined,
                            max: undefined
                        });
                        break;
                    case "date_picker":
                        defaultConditionType = "date_range";
                        methods.setValue(`${fieldArrayName}.${index}.condition_type`, defaultConditionType);
                        methods.setValue(`${fieldArrayName}.${index}.condition_value`, {
                            operator: "greater_than",
                            days_from_today: undefined
                        });
                        break;
                    case "single_select":
                    case "multi_select":
                        defaultConditionType = "in";
                        methods.setValue(`${fieldArrayName}.${index}.condition_type`, defaultConditionType);
                        methods.setValue(`${fieldArrayName}.${index}.condition_value`, []);
                        break;
                    default:
                        break;
                }
            }
        });
    }, [watchedDependencies, availableQuestions, methods, fieldArrayName]);

    return (
        <div className="space-y-4">
            {(watchedDependencies || []).map((dependency: ConditionDependency, index: number) => {
                const questionId = dependency.question_id?.id;

                const stableKey = dependency.id || `${questionId || "unknown"}-${index}`;
                return (
                    <div key={stableKey} className="relative space-y-2 rounded-lg border p-4">
                        <Button
                            variant="ghost"
                            size="icon"
                            className="absolute left-2 top-2 hover:bg-transparent"
                            onClick={() => onRemove(index)}
                        >
                            <X className="ml-2 h-4 w-4" />
                        </Button>

                        <SingleSelect
                            name={`${fieldArrayName}.${index}.question_id`}
                            label={TEXTS.selectQuestionLabel}
                            placeholder={TEXTS.selectQuestionPlaceholder}
                            options={filteredAvailableQuestions.map((question) => ({
                                id: question.id,
                                label:
                                    question.metadata?.label ||
                                    question.metadata?.placeholder ||
                                    `Question ID: ${question.id}`,
                                subtitle: question.groups_question?.name || ""
                            }))}
                            showSearch={true}
                        />

                        {questionId &&
                            renderSpecificConditionFields(
                                dependency,
                                index,
                                availableQuestions,
                                methods,
                                fieldArrayName
                            )}
                    </div>
                );
            })}

            <Button type="button" variant="outline" className="w-full" onClick={onAdd}>
                <PlusCircle className="ml-2 h-4 w-4" />
                {TEXTS.addDependencyButton}
            </Button>
        </div>
    );
};
