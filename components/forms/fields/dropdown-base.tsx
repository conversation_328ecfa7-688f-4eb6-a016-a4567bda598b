import { ChevronsUpDown } from "lucide-react";
import { ReactNode, useState } from "react";
import { useFormContext } from "react-hook-form";

import { BaseFieldProps } from "@/components/forms/fields/base-field";
import { Button } from "@/components/ui/button";
import { Command, CommandEmpty, CommandInput } from "@/components/ui/command";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { TooltipIcon } from "@/components/ui/tooltip-icon";
import { cn } from "@/lib/utils";

export interface Option {
    id: string;
    label: string;
    subtitle?: string;
}

export interface DropdownBaseProps extends BaseFieldProps {
    options: Option[];
    placeholder?: string;
    searchPlaceholder?: string;
    noResultsText?: string;
    displayValue: ReactNode;
    showSearch?: boolean;
    children: (
        filteredOptions: Option[],
        helpers: {
            open: boolean;
            setOpen: (open: boolean) => void;
            searchQuery: string;
            setSearchQuery: (query: string) => void;
        }
    ) => ReactNode;
}

const TEXTS = {
    search: "חיפוש...",
    noResults: "לא נמצאו תוצאות"
};

export function DropdownBase({
    name,
    label,
    placeholder,
    options,
    tooltip,
    searchPlaceholder = TEXTS.search,
    noResultsText = TEXTS.noResults,
    displayValue,
    showSearch = false,
    children
}: DropdownBaseProps) {
    const {
        formState: { errors, isSubmitted }
    } = useFormContext();

    const [open, setOpen] = useState(false);
    const [searchQuery, setSearchQuery] = useState("");

    const normalizeText = (text: string) => text.normalize("NFKC").trim();

    const textIncludes = (text: string, search: string) => {
        const normalizedText = normalizeText(text);
        const normalizedSearch = normalizeText(search);
        return normalizedText.indexOf(normalizedSearch) !== -1;
    };

    const filteredOptions =
        searchQuery === ""
            ? options
            : options.filter((option) => {
                  const matchesLabel = textIncludes(option.label, searchQuery);
                  const matchesSubtitle = option.subtitle ? textIncludes(option.subtitle, searchQuery) : false;
                  return matchesLabel || matchesSubtitle;
              });

    return (
        <div className="space-y-2" dir="rtl">
            {label && (
                <div className="flex items-center justify-start gap-1">
                    {tooltip && <TooltipIcon text={tooltip} />}
                    <Label htmlFor={name} className="text-right block">
                        {label}
                    </Label>
                </div>
            )}
            <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                    <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={open}
                        className={cn(
                            "w-full justify-between min-h-10 h-auto py-2 font-normal",
                            isSubmitted && errors[name] && "border-red-500 focus-visible:ring-red-500"
                        )}
                    >
                        <div className="w-full text-right">
                            {displayValue ? (
                                displayValue
                            ) : (
                                <span className="text-muted-foreground text-base md:text-sm">{placeholder}</span>
                            )}
                        </div>
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50 self-center" />
                    </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
                    <Command className="w-full" dir="rtl" shouldFilter={false}>
                        {showSearch && (
                            <CommandInput
                                placeholder={searchPlaceholder}
                                value={searchQuery}
                                onValueChange={setSearchQuery}
                                className="h-9 px-3 text-right placeholder:text-right [&::-webkit-input-placeholder]:text-right [-ms-input-placeholder]:text-right [&::placeholder]:text-right"
                                dir="rtl"
                            />
                        )}
                        {children(filteredOptions, { open, setOpen, searchQuery, setSearchQuery })}
                        {filteredOptions.length === 0 && (
                            <CommandEmpty className="py-6 text-center text-sm">{noResultsText}</CommandEmpty>
                        )}
                    </Command>
                </PopoverContent>
            </Popover>
        </div>
    );
}
