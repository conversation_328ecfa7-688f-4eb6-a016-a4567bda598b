import { Check } from "lucide-react";
import React from "react";
import { useFormContext } from "react-hook-form";

import { BaseFieldProps } from "@/components/forms/fields/base-field";
import { DropdownBase, Option } from "@/components/forms/fields/dropdown-base";
import { CommandGroup, CommandItem, CommandList } from "@/components/ui/command";
import { cn } from "@/lib/utils";

export interface SingleSelectFieldProps extends BaseFieldProps {
    options: Option[];
    placeholder?: string;
    requiredText?: string;
    searchPlaceholder?: string;
    noResultsText?: string;
    showSearch?: boolean;
}

const TEXTS = {
    required: "יש לבחור אפשרות",
    placeholder: "בחר אפשרות"
};

export function SingleSelect({
    name,
    label,
    placeholder = TEXTS.placeholder,
    options,
    required = true,
    requiredText = TEXTS.required,
    tooltip,
    searchPlaceholder,
    noResultsText,
    showSearch = false
}: SingleSelectFieldProps) {
    const {
        register,
        setValue,
        watch,
        formState: { errors, isSubmitted }
    } = useFormContext();

    const value = watch(name);

    const handleSelect = (optionId: string, setOpen: (open: boolean) => void) => {
        const selectedOption = options.find((option) => option.id === optionId);
        if (selectedOption) {
            setValue(
                name,
                { id: selectedOption.id, label: selectedOption.label },
                { shouldValidate: true, shouldTouch: true }
            );
        }
        setOpen(false);
    };

    const displayValue = value?.label;

    return (
        <>
            <DropdownBase
                name={name}
                label={label}
                placeholder={placeholder}
                options={options}
                tooltip={tooltip}
                searchPlaceholder={searchPlaceholder}
                noResultsText={noResultsText}
                displayValue={displayValue}
                showSearch={showSearch}
            >
                {(filteredOptions, { setOpen }) => (
                    <CommandList>
                        <CommandGroup>
                            {filteredOptions.map((option) => (
                                <CommandItem
                                    key={option.id}
                                    value={option.id}
                                    onSelect={() => handleSelect(option.id, setOpen)}
                                    className="w-full px-2 py-1.5 cursor-pointer"
                                >
                                    <div className="flex items-center w-full">
                                        <div className="flex flex-col items-start flex-1">
                                            <span className="text-sm">{option.label}</span>
                                            {option.subtitle && (
                                                <span className="text-xs text-muted-foreground">{option.subtitle}</span>
                                            )}
                                        </div>
                                        <Check
                                            className={cn(
                                                "h-4 w-4 flex-shrink-0 mr-2",
                                                (() => {
                                                    return value?.id === option.id ? "opacity-100" : "opacity-0";
                                                })()
                                            )}
                                        />
                                    </div>
                                </CommandItem>
                            ))}
                        </CommandGroup>
                    </CommandList>
                )}
            </DropdownBase>
            <input type="hidden" {...register(name, { required: required ? requiredText : undefined })} />
            {isSubmitted && errors[name] && (
                <p className="text-sm text-red-500 text-right">{errors[name]?.message as string}</p>
            )}
        </>
    );
}
