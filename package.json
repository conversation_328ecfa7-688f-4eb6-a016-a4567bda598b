{"private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint --fix", "lint:md": "eslint --ext md .clinerules", "format:all": "prettier --write .", "format:staged": "prettier --write $(git diff --staged --name-only --diff-filter d | xargs)", "prepare": "husky", "generate:types": "npx supabase gen types typescript --local > types/database.types.ts", "test": "jest --verbose", "test:coverage": "jest --coverage", "test:report": "jest --coverage --coverageReporters=text-summary", "test:ci": "jest --coverage --watchAll=false"}, "dependencies": {"@clerk/elements": "^0.23.37", "@clerk/localizations": "^3.15.1", "@clerk/nextjs": "^6.19.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@react-email/components": "^0.0.33", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "autoprefixer": "10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "fireworks-js": "^2.10.8", "framer-motion": "^11.0.8", "input-otp": "^1.4.2", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "lucide-react": "^0.484.0", "next": "^15.2.0", "next-themes": "^0.4.6", "posthog-js": "^1.256.1", "posthog-node": "^5.1.1", "prettier": "^3.5.3", "react": "19.0.0", "react-colorful": "^5.6.1", "react-day-picker": "8.10.1", "react-dom": "19.0.0", "react-fast-marquee": "^1.6.5", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-pdf": "^9.2.1", "react-share": "^5.2.2", "recharts": "^2.15.1", "resend": "^4.1.2", "sonner": "^2.0.1", "svix": "^1.67.0", "uuid": "^11.1.0", "vaul": "^1.1.2"}, "devDependencies": {"@eslint/js": "^9.26.0", "@next/eslint-plugin-next": "^15.3.2", "@stagewise/toolbar-next": "^0.5.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/dompurify": "^3.2.0", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.16", "@types/node": "22.10.2", "@types/react": "^19.0.2", "@types/react-dom": "19.0.2", "@types/uuid": "^10.0.0", "eslint": "^9.21.0", "eslint-config-next": "^15.2.1", "eslint-config-prettier": "^10.0.2", "eslint-plugin-markdown": "^5.1.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.2", "jest-environment-jsdom": "^30.0.2", "jsdom": "^26.1.0", "postcss": "8.4.49", "supabase": "2.20.5", "tailwind-merge": "^2.5.2", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.4.0", "typescript": "5.7.2"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}