import { upsertAnswers, fetchQuestionsData } from "@/app/actions/dynamic-questions-form-actions";
import { getSupabaseClient } from "@/utils/supabase/client";

jest.mock("@/app/actions/collaboration-send-actions", () => ({
    sendCollaborationData: jest.fn().mockResolvedValue({ success: true })
}));

jest.mock("@/utils/supabase/client", () => ({
    getSupabaseClient: jest.fn()
}));

jest.mock("@clerk/nextjs/server", () => ({
    auth: jest.fn(() => ({
        userId: "user-123",
        sessionClaims: { organizations: { "org-1": "org:admin" } }
    }))
}));

jest.mock("@/lib/org-role", () => ({
    ...jest.requireActual("@/lib/org-role"),
    isAdminFromSessionClaims: jest.fn(() => true)
}));

let consoleErrorSpy: jest.SpyInstance;

beforeAll(() => {
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
});

afterAll(() => {
    consoleErrorSpy.mockRestore();
});

beforeEach(() => {
    jest.clearAllMocks();
});

describe("upsertAnswers", () => {
    const userId = "user-123";
    const answers = [
        {
            question_id: "q1",
            user_id: userId,
            answer: "Test answer 1"
        },
        {
            question_id: "q2",
            user_id: userId,
            answer: "Test answer 2"
        }
    ];

    it("should successfully upsert answers", async () => {
        const mockSupabaseClient = {
            from: jest.fn(() => ({
                delete: jest.fn(() => ({
                    eq: jest.fn(() => ({
                        in: jest.fn(() => Promise.resolve({ error: null }))
                    }))
                })),
                upsert: jest.fn(() => Promise.resolve({ error: null }))
            }))
        };

        (getSupabaseClient as jest.Mock).mockReturnValue(mockSupabaseClient);

        const result = await upsertAnswers(userId, answers, ["hidden-q1"]);

        expect(result.success).toBe(true);
    });

    it("should handle empty answers array", async () => {
        const mockSupabaseClient = {
            from: jest.fn(() => ({
                delete: jest.fn(() => ({
                    eq: jest.fn(() => ({
                        in: jest.fn(() => Promise.resolve({ error: null }))
                    }))
                }))
            }))
        };

        (getSupabaseClient as jest.Mock).mockReturnValue(mockSupabaseClient);

        const result = await upsertAnswers(userId, [], ["hidden-q1"]);

        expect(result.success).toBe(true);
    });

    it("should handle empty hidden questions array", async () => {
        const mockSupabaseClient = {
            from: jest.fn(() => ({
                upsert: jest.fn(() => Promise.resolve({ error: null }))
            }))
        };

        (getSupabaseClient as jest.Mock).mockReturnValue(mockSupabaseClient);

        const result = await upsertAnswers(userId, answers, []);

        expect(result.success).toBe(true);
    });

    it("should return error when deletion fails", async () => {
        const mockSupabaseClient = {
            from: jest.fn(() => ({
                delete: jest.fn(() => ({
                    eq: jest.fn(() => ({
                        in: jest.fn(() => Promise.resolve({ error: new Error("Delete failed") }))
                    }))
                }))
            }))
        };

        (getSupabaseClient as jest.Mock).mockReturnValue(mockSupabaseClient);

        const result = await upsertAnswers(userId, answers, ["hidden-q1"]);

        expect(result.success).toBe(false);
        expect(result.error).toBe("Delete failed");
        expect(consoleErrorSpy).toHaveBeenCalled();
    });

    it("should return error when upsert fails", async () => {
        const mockSupabaseClient = {
            from: jest
                .fn()
                .mockReturnValueOnce({
                    delete: jest.fn(() => ({
                        eq: jest.fn(() => ({
                            in: jest.fn(() => Promise.resolve({ error: null }))
                        }))
                    }))
                })
                .mockReturnValueOnce({
                    upsert: jest.fn(() => Promise.resolve({ error: new Error("Upsert failed") }))
                })
        };

        (getSupabaseClient as jest.Mock).mockReturnValue(mockSupabaseClient);

        const result = await upsertAnswers(userId, answers, ["hidden-q1"]);

        expect(result.success).toBe(false);
        expect(result.error).toBe("Upsert failed");
        expect(consoleErrorSpy).toHaveBeenCalled();
    });

    it("should handle collaboration error gracefully", async () => {
        const { sendCollaborationData } = require("@/app/actions/collaboration-send-actions");
        sendCollaborationData.mockRejectedValue(new Error("Collaboration failed"));

        const mockSupabaseClient = {
            from: jest.fn(() => ({
                upsert: jest.fn(() => Promise.resolve({ error: null }))
            }))
        };

        (getSupabaseClient as jest.Mock).mockReturnValue(mockSupabaseClient);

        const result = await upsertAnswers(userId, answers, []);

        expect(result.success).toBe(true);
        expect(consoleErrorSpy).toHaveBeenCalledWith("Error sending data to collaborations:", expect.any(Error));
    });
});

describe("fetchQuestionsData", () => {
    const sections = ["personal_details", "education"];
    const userId = "user-123";

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should fetch questions data without scholarship ID", async () => {
        const mockSupabaseClient = {
            from: jest
                .fn()
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() =>
                            Promise.resolve({
                                data: [{ id: "q1", group_id: "g1", section: "personal_details" }],
                                error: null
                            })
                        )
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() => ({
                            order: jest.fn(() =>
                                Promise.resolve({
                                    data: [
                                        {
                                            id: "q1",
                                            type: "short_text",
                                            metadata: { label: "Question 1" },
                                            section: "personal_details",
                                            group_id: "g1",
                                            created_at: "2024-01-01",
                                            updated_at: "2024-01-01"
                                        }
                                    ],
                                    error: null
                                })
                            )
                        }))
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() => ({
                            order: jest.fn(() =>
                                Promise.resolve({
                                    data: [
                                        {
                                            id: "g1",
                                            name: "Group 1",
                                            description: null,
                                            created_at: "2024-01-01",
                                            updated_at: "2024-01-01"
                                        }
                                    ],
                                    error: null
                                })
                            )
                        }))
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() => ({
                            eq: jest.fn(() =>
                                Promise.resolve({
                                    data: [
                                        {
                                            id: "a1",
                                            user_id: userId,
                                            question_id: "q1",
                                            answer: "answer1",
                                            created_at: "2024-01-01"
                                        }
                                    ],
                                    error: null
                                })
                            )
                        }))
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() =>
                            Promise.resolve({
                                data: [],
                                error: null
                            })
                        )
                    }))
                })
        };

        (getSupabaseClient as jest.Mock).mockReturnValue(mockSupabaseClient);

        const result = await fetchQuestionsData(sections, userId);

        expect(result.success).toBe(true);
        expect(result.data?.questions).toHaveLength(1);
        expect(result.data?.questions[0].metadata.label).toBe("Question 1");
        expect(result.data?.questionGroups["g1"]).toEqual({
            id: "g1",
            name: "Group 1",
            description: undefined,
            created_at: "2024-01-01",
            updated_at: "2024-01-01"
        });
    });

    it("should fetch questions data with scholarship ID", async () => {
        const scholarshipId = "scholarship-123";
        const mockSupabaseClient = {
            from: jest
                .fn()
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        eq: jest.fn(() =>
                            Promise.resolve({
                                data: [{ questions: { id: "q1", group_id: "g1", section: "personal_details" } }],
                                error: null
                            })
                        )
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() => ({
                            order: jest.fn(() =>
                                Promise.resolve({
                                    data: [
                                        {
                                            id: "q1",
                                            type: "short_text",
                                            metadata: { label: "Scholarship Question" },
                                            section: "personal_details",
                                            group_id: "g1",
                                            created_at: "2024-01-01",
                                            updated_at: "2024-01-01"
                                        }
                                    ],
                                    error: null
                                })
                            )
                        }))
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() => ({
                            order: jest.fn(() =>
                                Promise.resolve({
                                    data: [],
                                    error: null
                                })
                            )
                        }))
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() => ({
                            eq: jest.fn(() =>
                                Promise.resolve({
                                    data: [],
                                    error: null
                                })
                            )
                        }))
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() =>
                            Promise.resolve({
                                data: [],
                                error: null
                            })
                        )
                    }))
                })
        };

        (getSupabaseClient as jest.Mock).mockReturnValue(mockSupabaseClient);

        const result = await fetchQuestionsData(sections, userId, scholarshipId);

        expect(result.success).toBe(true);
        expect(result.data?.questions).toHaveLength(1);
    });

    it("should handle no questions found", async () => {
        const mockSupabaseClient = {
            from: jest.fn(() => ({
                select: jest.fn(() => ({
                    in: jest.fn(() =>
                        Promise.resolve({
                            data: [],
                            error: null
                        })
                    )
                }))
            }))
        };

        (getSupabaseClient as jest.Mock).mockReturnValue(mockSupabaseClient);

        const result = await fetchQuestionsData(sections, userId);

        expect(result.success).toBe(true);
        expect(result.data?.questions).toEqual([]);
        expect(result.data?.questionGroups).toEqual({});
        expect(result.data?.conditions).toEqual([]);
        expect(result.data?.questionConditionLinks).toEqual([]);
        expect(result.data?.answers).toEqual([]);
    });

    it("should handle conditions with links", async () => {
        const mockSupabaseClient = {
            from: jest
                .fn()
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() =>
                            Promise.resolve({
                                data: [{ id: "q1", group_id: "g1", section: "personal_details" }],
                                error: null
                            })
                        )
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() => ({
                            order: jest.fn(() =>
                                Promise.resolve({
                                    data: [
                                        {
                                            id: "q1",
                                            type: "short_text",
                                            metadata: { label: "Question 1" },
                                            section: "personal_details",
                                            group_id: "g1",
                                            created_at: "2024-01-01",
                                            updated_at: "2024-01-01"
                                        }
                                    ],
                                    error: null
                                })
                            )
                        }))
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() => ({
                            order: jest.fn(() =>
                                Promise.resolve({
                                    data: [],
                                    error: null
                                })
                            )
                        }))
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() => ({
                            eq: jest.fn(() =>
                                Promise.resolve({
                                    data: [],
                                    error: null
                                })
                            )
                        }))
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() =>
                            Promise.resolve({
                                data: [
                                    {
                                        question_id: "q1",
                                        condition_id: "c1",
                                        id: "link1",
                                        created_at: "2024-01-01",
                                        updated_at: "2024-01-01"
                                    }
                                ],
                                error: null
                            })
                        )
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() =>
                            Promise.resolve({
                                data: [
                                    {
                                        id: "c1",
                                        question_id: "q1",
                                        type: "in",
                                        value: ["option1", "option2"],
                                        created_at: "2024-01-01",
                                        updated_at: "2024-01-01"
                                    }
                                ],
                                error: null
                            })
                        )
                    }))
                })
        };

        (getSupabaseClient as jest.Mock).mockReturnValue(mockSupabaseClient);

        const result = await fetchQuestionsData(sections, userId);

        expect(result.success).toBe(true);
        expect(result.data?.conditions).toHaveLength(1);
        expect(result.data?.conditions[0].value).toEqual(["option1", "option2"]);
        expect(result.data?.questionConditionLinks).toHaveLength(1);
    });

    it("should handle null metadata gracefully", async () => {
        const mockSupabaseClient = {
            from: jest
                .fn()
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() =>
                            Promise.resolve({
                                data: [{ id: "q1", group_id: "g1", section: "personal_details" }],
                                error: null
                            })
                        )
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() => ({
                            order: jest.fn(() =>
                                Promise.resolve({
                                    data: [
                                        {
                                            id: "q1",
                                            type: "short_text",
                                            metadata: null,
                                            section: "personal_details",
                                            group_id: "g1",
                                            created_at: "2024-01-01",
                                            updated_at: "2024-01-01"
                                        }
                                    ],
                                    error: null
                                })
                            )
                        }))
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() => ({
                            order: jest.fn(() =>
                                Promise.resolve({
                                    data: [],
                                    error: null
                                })
                            )
                        }))
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() => ({
                            eq: jest.fn(() =>
                                Promise.resolve({
                                    data: [],
                                    error: null
                                })
                            )
                        }))
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() =>
                            Promise.resolve({
                                data: [],
                                error: null
                            })
                        )
                    }))
                })
        };

        (getSupabaseClient as jest.Mock).mockReturnValue(mockSupabaseClient);

        const result = await fetchQuestionsData(sections, userId);

        expect(result.success).toBe(true);
        expect(result.data?.questions[0].metadata.label).toBe("");
    });

    it("should return error when initial question fetch fails", async () => {
        const mockSupabaseClient = {
            from: jest.fn(() => ({
                select: jest.fn(() => ({
                    in: jest.fn(() =>
                        Promise.resolve({
                            data: null,
                            error: new Error("Questions fetch failed")
                        })
                    )
                }))
            }))
        };

        (getSupabaseClient as jest.Mock).mockReturnValue(mockSupabaseClient);

        const result = await fetchQuestionsData(sections, userId);

        expect(result.success).toBe(false);
        expect(result.error).toBe("Questions fetch failed");
        expect(consoleErrorSpy).toHaveBeenCalled();
    });

    it("should return error when questions detail fetch fails", async () => {
        const mockSupabaseClient = {
            from: jest
                .fn()
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() =>
                            Promise.resolve({
                                data: [{ id: "q1", group_id: "g1", section: "personal_details" }],
                                error: null
                            })
                        )
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() => ({
                            order: jest.fn(() =>
                                Promise.resolve({
                                    data: null,
                                    error: new Error("Questions detail fetch failed")
                                })
                            )
                        }))
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() => ({
                            order: jest.fn(() =>
                                Promise.resolve({
                                    data: [],
                                    error: null
                                })
                            )
                        }))
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() => ({
                            eq: jest.fn(() =>
                                Promise.resolve({
                                    data: [],
                                    error: null
                                })
                            )
                        }))
                    }))
                })
                .mockReturnValueOnce({
                    select: jest.fn(() => ({
                        in: jest.fn(() =>
                            Promise.resolve({
                                data: [],
                                error: null
                            })
                        )
                    }))
                })
        };

        (getSupabaseClient as jest.Mock).mockReturnValue(mockSupabaseClient);

        const result = await fetchQuestionsData(sections, userId);

        expect(result.success).toBe(false);
        expect(result.error).toBe("Questions detail fetch failed");
        expect(consoleErrorSpy).toHaveBeenCalled();
    });
});
