import React from "react";
import { render, screen, fireEvent, cleanup, waitFor } from "@testing-library/react";

const TEXTS = {
    dialogTitle: "מסמך לדוגמא",
    loadingText: "טוען רשימת מסמכים...",
    errorText: "לא ניתן להציג כרגע."
};

// Mock the entire component module to avoid import.meta.url issues
jest.mock("@/components/common/pdf-example-modal", () => ({
    PdfExampleModal: jest.fn(({ open, url, onOpenChange }) => {
        const [numPages, setNumPages] = React.useState(1);

        React.useEffect(() => {
            if (!open) setNumPages(1);
        }, [open]);

        React.useEffect(() => {
            if (url && open) {
                setTimeout(() => setNumPages(3), 100);
            }
        }, [url, open]);

        return (
            <div data-testid="dialog" data-open={open}>
                {open && (
                    <div
                        data-testid="dialog-content"
                        className="max-w-full w-[95vw] sm:max-w-2xl p-0 flex flex-col items-center"
                    >
                        <h2 data-testid="dialog-title" className="w-full px-6 pt-6 pb-2 text-center">
                            {TEXTS.dialogTitle}
                        </h2>
                        <div
                            className="w-full flex-1 overflow-auto flex justify-center items-center px-2 pb-6"
                            style={{ minHeight: 400, maxHeight: "80vh" }}
                            data-testid="pdf-container"
                        >
                            {url && (
                                <div data-testid="pdf-document">
                                    <div dir="rtl" className="text-center w-full py-8">
                                        {TEXTS.loadingText}
                                    </div>
                                    <div dir="rtl" className="text-center w-full py-8 text-red-500">
                                        {TEXTS.errorText}
                                    </div>
                                    {Array.from(new Array(numPages), (el, index) => (
                                        <div
                                            key={`page_${index + 1}`}
                                            data-testid={`pdf-page-${index + 1}`}
                                            data-width={
                                                typeof window !== "undefined" && window.innerWidth < 600
                                                    ? window.innerWidth - 32
                                                    : 500
                                            }
                                        >
                                            Page {index + 1}
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                )}
                <button data-testid="dialog-close" onClick={() => onOpenChange(false)}>
                    Close
                </button>
            </div>
        );
    })
}));

// Import the mocked component
const { PdfExampleModal } = require("@/components/common/pdf-example-modal");

// Mock window.innerWidth for responsive testing
const mockInnerWidth = (width: number) => {
    Object.defineProperty(window, "innerWidth", {
        writable: true,
        configurable: true,
        value: width
    });
};

describe("PdfExampleModal", () => {
    const defaultProps = {
        open: false,
        url: "https://example.com/test.pdf",
        onOpenChange: jest.fn()
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockInnerWidth(1024);
    });

    afterEach(cleanup);

    it("renders dialog with correct open state", () => {
        render(<PdfExampleModal {...defaultProps} open={true} />);

        expect(screen.getByTestId("dialog")).toBeInTheDocument();
        expect(screen.getByTestId("dialog")).toHaveAttribute("data-open", "true");
    });

    it("does not render dialog content when closed", () => {
        render(<PdfExampleModal {...defaultProps} open={false} />);

        expect(screen.getByTestId("dialog")).toHaveAttribute("data-open", "false");
        expect(screen.queryByTestId("dialog-content")).not.toBeInTheDocument();
    });

    it("renders dialog title with correct text", () => {
        render(<PdfExampleModal {...defaultProps} open={true} />);

        expect(screen.getByTestId("dialog-title")).toHaveTextContent(TEXTS.dialogTitle);
    });

    it("renders PDF document when URL is provided", () => {
        render(<PdfExampleModal {...defaultProps} open={true} />);

        expect(screen.getByTestId("pdf-document")).toBeInTheDocument();
    });

    it("does not render PDF document when URL is null", () => {
        render(<PdfExampleModal {...defaultProps} open={true} url={null} />);

        expect(screen.queryByTestId("pdf-document")).not.toBeInTheDocument();
    });

    it("renders loading state text", () => {
        render(<PdfExampleModal {...defaultProps} open={true} />);

        expect(screen.getByText(TEXTS.loadingText)).toBeInTheDocument();
    });

    it("renders error state text", () => {
        render(<PdfExampleModal {...defaultProps} open={true} />);

        expect(screen.getByText(TEXTS.errorText)).toBeInTheDocument();
    });

    it("calls onOpenChange when dialog close is triggered", () => {
        const mockOnOpenChange = jest.fn();
        render(<PdfExampleModal {...defaultProps} open={true} onOpenChange={mockOnOpenChange} />);

        fireEvent.click(screen.getByTestId("dialog-close"));

        expect(mockOnOpenChange).toHaveBeenCalledWith(false);
    });

    it("has correct dialog content styling", () => {
        render(<PdfExampleModal {...defaultProps} open={true} />);

        const dialogContent = screen.getByTestId("dialog-content");
        expect(dialogContent).toHaveClass("max-w-full");
        expect(dialogContent).toHaveClass("w-[95vw]");
        expect(dialogContent).toHaveClass("sm:max-w-2xl");
        expect(dialogContent).toHaveClass("p-0");
        expect(dialogContent).toHaveClass("flex");
        expect(dialogContent).toHaveClass("flex-col");
        expect(dialogContent).toHaveClass("items-center");
    });

    it("has correct dialog title styling", () => {
        render(<PdfExampleModal {...defaultProps} open={true} />);

        const dialogTitle = screen.getByTestId("dialog-title");
        expect(dialogTitle).toHaveClass("w-full");
        expect(dialogTitle).toHaveClass("px-6");
        expect(dialogTitle).toHaveClass("pt-6");
        expect(dialogTitle).toHaveClass("pb-2");
        expect(dialogTitle).toHaveClass("text-center");
    });

    it("has correct PDF container styling and inline styles", () => {
        render(<PdfExampleModal {...defaultProps} open={true} />);

        const pdfContainer = screen.getByTestId("pdf-container");
        expect(pdfContainer).toHaveClass("w-full");
        expect(pdfContainer).toHaveClass("flex-1");
        expect(pdfContainer).toHaveClass("overflow-auto");
        expect(pdfContainer).toHaveClass("flex");
        expect(pdfContainer).toHaveClass("justify-center");
        expect(pdfContainer).toHaveClass("items-center");
        expect(pdfContainer).toHaveClass("px-2");
        expect(pdfContainer).toHaveClass("pb-6");
        expect(pdfContainer).toHaveStyle("min-height: 400px");
        expect(pdfContainer).toHaveStyle("max-height: 80vh");
    });

    it("uses desktop width for pages on large screens", async () => {
        mockInnerWidth(1024);

        render(<PdfExampleModal {...defaultProps} open={true} />);

        await waitFor(() => {
            expect(screen.getByTestId("pdf-page-1")).toHaveAttribute("data-width", "500");
        });
    });

    it("uses mobile width for pages on small screens", async () => {
        mockInnerWidth(400);

        render(<PdfExampleModal {...defaultProps} open={true} />);

        const expectedWidth = (400 - 32).toString();
        await waitFor(() => {
            expect(screen.getByTestId("pdf-page-1")).toHaveAttribute("data-width", expectedWidth);
        });
    });

    it("handles URL change while modal is open", () => {
        const { rerender } = render(<PdfExampleModal {...defaultProps} open={true} url="first.pdf" />);

        expect(screen.getByTestId("pdf-document")).toBeInTheDocument();

        rerender(<PdfExampleModal {...defaultProps} open={true} url="second.pdf" />);

        expect(screen.getByTestId("pdf-document")).toBeInTheDocument();
    });

    it("handles opening modal with no URL initially", () => {
        render(<PdfExampleModal {...defaultProps} open={true} url={null} />);

        expect(screen.queryByTestId("pdf-document")).not.toBeInTheDocument();
    });

    it("maintains RTL direction for loading text", () => {
        render(<PdfExampleModal {...defaultProps} open={true} />);

        const loadingElement = screen.getByText(TEXTS.loadingText).closest("div");
        expect(loadingElement).toHaveAttribute("dir", "rtl");
        expect(loadingElement).toHaveClass("text-center");
        expect(loadingElement).toHaveClass("w-full");
        expect(loadingElement).toHaveClass("py-8");
    });

    it("maintains RTL direction for error text", () => {
        render(<PdfExampleModal {...defaultProps} open={true} />);

        const errorElement = screen.getByText(TEXTS.errorText).closest("div");
        expect(errorElement).toHaveAttribute("dir", "rtl");
        expect(errorElement).toHaveClass("text-center");
        expect(errorElement).toHaveClass("w-full");
        expect(errorElement).toHaveClass("py-8");
        expect(errorElement).toHaveClass("text-red-500");
    });

    it("renders multiple pages when PDF loads successfully", async () => {
        render(<PdfExampleModal {...defaultProps} open={true} />);

        await waitFor(() => {
            expect(screen.getByTestId("pdf-page-1")).toBeInTheDocument();
            expect(screen.getByTestId("pdf-page-2")).toBeInTheDocument();
            expect(screen.getByTestId("pdf-page-3")).toBeInTheDocument();
        });
    });

    it("resets pages when modal closes", () => {
        const { rerender } = render(<PdfExampleModal {...defaultProps} open={true} />);

        // Close modal
        rerender(<PdfExampleModal {...defaultProps} open={false} />);

        // Reopen modal - should start with 1 page again (before PDF loads)
        rerender(<PdfExampleModal {...defaultProps} open={true} />);

        expect(screen.getByTestId("pdf-page-1")).toBeInTheDocument();
        expect(screen.queryByTestId("pdf-page-2")).not.toBeInTheDocument();
    });

    it("handles edge case of very small screen width", async () => {
        mockInnerWidth(300);

        render(<PdfExampleModal {...defaultProps} open={true} />);

        const expectedWidth = (300 - 32).toString();
        await waitFor(() => {
            expect(screen.getByTestId("pdf-page-1")).toHaveAttribute("data-width", expectedWidth);
        });
    });
});
