import { createBrowserClient } from "@supabase/ssr";

// Add Clerk to Window to avoid type errors
declare global {
    interface Window {
        Clerk: any;
    }
}

function createClerkSupabaseClient() {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string;
    const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string;

    if (!supabaseUrl || !anonKey) {
        throw new Error("Supabase URL and anon key must be provided");
    }

    return createBrowserClient(supabaseUrl, anonKey, {
        global: {
            // Get the Supabase token with a custom fetch method
            fetch: async (url, options = {}) => {
                const clerkToken = await window.Clerk?.session?.getToken({
                    template: "supabase"
                });

                // Construct fetch headers
                const headers = new Headers(options?.headers);
                if (clerkToken) {
                    headers.set("Authorization", `Bearer ${clerkToken}`);
                }

                // Now call the default fetch
                return fetch(url, {
                    ...options,
                    headers
                });
            }
        }
    });
}

export const createBrowserSupabaseClient = createClerkSupabaseClient;

let supabaseInstance: ReturnType<typeof createClerkSupabaseClient>;

export const getSupabaseClient = () => {
    if (!supabaseInstance) {
        try {
            supabaseInstance = createClerkSupabaseClient();
        } catch (error) {
            console.error("Failed to create Supabase client:", error);
            throw new Error(
                "Could not initialize Supabase client. Please check your environment variables and connection."
            );
        }
    }
    return supabaseInstance;
};
