import { ConditionDependency, ConditionQuestion } from "@/components/forms/fields/condition-selector";
import { Option } from "@/components/forms/fields/dropdown-base";
import { mapIdsToOptions, mapIdToOption } from "@/utils/form-utils";

/**
 * Represents a condition dependency as stored in the database
 */
export interface BackendConditionDependency {
    question_id: string;
    condition_type: "in" | "range" | "date_range";
    condition_value: any;
    id?: string;
}

/**
 * Transforms database condition dependencies to form-compatible format
 * This includes:
 * - Converting question_id string to Option object with proper label
 * - Converting condition values for "in" type from string[] to Option[] for MultiSelect compatibility
 * - Preserving other condition types (range, date_range) as-is
 */
export function transformConditionsFromDatabase(
    conditionsFromDB: BackendConditionDependency[],
    availableQuestions: ConditionQuestion[]
): ConditionDependency[] {
    return conditionsFromDB.map((condition: BackendConditionDependency) => {
        // Use form utilities to map question ID to Option object with proper label
        const questionOption = mapIdToOption(
            condition.question_id,
            availableQuestions,
            (question: ConditionQuestion) => question.id,
            (question: ConditionQuestion) => question.metadata?.label || question.metadata?.placeholder,
            (id: string) => id
        );

        // Transform condition value for "in" type conditions that reference question options
        let transformedConditionValue = condition.condition_value;
        if (condition.condition_type === "in" && Array.isArray(condition.condition_value)) {
            const targetQuestion = availableQuestions.find((q) => q.id === condition.question_id);
            if (targetQuestion?.metadata?.options) {
                // Check if condition values are already Option objects or string IDs
                const firstValue = condition.condition_value[0];
                if (firstValue && typeof firstValue === "object" && "id" in firstValue) {
                    // Already Option objects, keep as is
                    transformedConditionValue = condition.condition_value;
                } else {
                    // String IDs that need to be mapped to Option objects
                    const conditionValueIds = condition.condition_value as unknown as string[];
                    transformedConditionValue = mapIdsToOptions(
                        conditionValueIds,
                        targetQuestion.metadata.options as Option[],
                        (opt: Option) => opt.id,
                        (opt: Option) => opt.label,
                        (id: string) => id
                    );
                }
            }
        }

        return {
            ...condition,
            question_id: questionOption,
            condition_value: transformedConditionValue
        };
    });
}

/**
 * Transforms form condition dependencies back to database format
 * This includes:
 * - Converting question_id Option object back to string ID
 * - Converting condition values for "in" type from Option[] back to string[] for database storage
 * - Preserving other condition types (range, date_range) as-is
 */
export function transformConditionsToDatabase(conditionsFromForm: ConditionDependency[]): BackendConditionDependency[] {
    return conditionsFromForm
        .filter((dep) => {
            // Filter out conditions without valid question ID
            return (
                dep.question_id &&
                dep.question_id.id &&
                typeof dep.question_id.id === "string" &&
                dep.question_id.id.trim() !== ""
            );
        })
        .map((dep) => {
            let dbConditionValue: any = dep.condition_value;

            // Convert Option[] back to string[] for "in" type conditions
            if (dep.condition_type === "in" && Array.isArray(dep.condition_value)) {
                const options = dep.condition_value as unknown as Option[];
                dbConditionValue = options.map((option: Option) => option.id) as string[];
            }

            return {
                question_id: dep.question_id.id,
                condition_type: dep.condition_type,
                condition_value: dbConditionValue
            };
        });
}
