# RLS Testing Guide for Clerk JWT Integration

This guide provides comprehensive testing procedures for the Row Level Security (RLS) implementation with Clerk <PERSON>WT authentication.

## 🚀 Quick Start

### 1. Apply the Migrations

First, apply all the RLS migrations to your Supabase database:

```bash
# Navigate to your project directory
cd your-project-directory

# Apply migrations (if using Supabase CLI)
supabase db push

# Or apply them manually in the Supabase dashboard
```

### 2. Configure Clerk JWT Template

Ensure your Clerk JWT template is properly configured:

1. Go to your Clerk Dashboard
2. Navigate to **JWT Templates**
3. Create or edit your **Supabase template**
4. Ensure the template includes:
   - `sub` claim (user ID)
   - `org_role` claim (for admin detection)

Example JWT template:
```json
{
  "sub": "{{user.id}}",
  "org_role": "{{user.organization_memberships.[0].role}}"
}
```

### 3. Environment Variables

Ensure these environment variables are set:

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Clerk
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your-clerk-publishable-key
CLERK_SECRET_KEY=your-clerk-secret-key
```

## 🧪 Testing Procedures

### Test 1: Basic RLS Setup Verification

Run the built-in test functions to verify RLS is properly configured:

```sql
-- Test RLS policies
SELECT * FROM test_rls_policies();

-- Check RLS status on all tables
SELECT * FROM check_rls_status();

-- List all RLS policies
SELECT * FROM list_rls_policies();
```

Expected results:
- All tests should return `PASS`
- All relevant tables should have `rls_enabled = true`
- Each table should have appropriate policies

### Test 2: Anonymous User Access

Test what anonymous users can access:

```sql
-- These should work (public data)
SELECT COUNT(*) FROM scholarships WHERE is_public = true AND is_active = true;
SELECT COUNT(*) FROM faq;
SELECT COUNT(*) FROM testimonials;
SELECT COUNT(*) FROM groups_scholarship;

-- These should return 0 rows or fail (protected data)
SELECT COUNT(*) FROM answers;
SELECT COUNT(*) FROM user_subscriptions;
SELECT COUNT(*) FROM banners;
SELECT COUNT(*) FROM collaborations;
```

### Test 3: Authenticated User Access

Test with a regular authenticated user (non-admin):

```sql
-- These should work for authenticated users
SELECT COUNT(*) FROM scholarships WHERE is_public = true AND is_active = true;
SELECT COUNT(*) FROM questions;
SELECT COUNT(*) FROM document_types;
SELECT COUNT(*) FROM conditions;

-- These should return only user's own data
SELECT COUNT(*) FROM answers WHERE user_id = requesting_user_id();
SELECT COUNT(*) FROM user_subscriptions WHERE user_id = requesting_user_id();
SELECT COUNT(*) FROM user_scholarship_applications WHERE user_id = requesting_user_id();

-- These should return 0 rows (admin-only)
SELECT COUNT(*) FROM banners;
SELECT COUNT(*) FROM collaborations;
SELECT COUNT(*) FROM coupons;
```

### Test 4: Admin User Access

Test with an admin user:

```sql
-- These should work for admins (full access)
SELECT COUNT(*) FROM banners;
SELECT COUNT(*) FROM collaborations;
SELECT COUNT(*) FROM scholarships;
SELECT COUNT(*) FROM questions;
SELECT COUNT(*) FROM answers;
SELECT COUNT(*) FROM user_subscriptions;
SELECT COUNT(*) FROM coupons;

-- Admin should see all data, not just their own
SELECT COUNT(*) FROM answers; -- Should show all answers
SELECT COUNT(*) FROM user_subscriptions; -- Should show all subscriptions
```

### Test 5: Insert/Update/Delete Operations

Test CRUD operations with different user contexts:

```sql
-- Test as regular user
INSERT INTO answers (user_id, question_id, answer) 
VALUES (requesting_user_id(), 'some-question-id', 'test answer');

-- Try to insert for another user (should fail)
INSERT INTO answers (user_id, question_id, answer) 
VALUES ('other-user-id', 'some-question-id', 'test answer');

-- Test admin operations
-- (Should work as admin, fail as regular user)
INSERT INTO banners (title, text, background_color, text_color) 
VALUES ('Test Banner', 'Test content', '#000000', '#ffffff');
```

## 🔧 Manual Testing in Browser

### Test the Client-Side Integration

1. **Login Flow**: 
   - Sign in with Clerk
   - Check browser network tab for JWT tokens in Supabase requests

2. **User-Specific Data**:
   - Navigate to user dashboard
   - Verify users only see their own data
   - Try accessing other users' data via direct API calls (should fail)

3. **Admin Access**:
   - Login as admin user
   - Navigate to admin dashboard
   - Verify admin can access all data

### Test Server Actions

1. **Form Submissions**:
   - Submit forms that trigger server actions
   - Verify data is properly filtered by RLS policies

2. **API Routes**:
   - Test API endpoints that use Supabase
   - Verify authentication tokens are properly passed

## 🚨 Common Issues and Solutions

### Issue 1: "JWT token not found" errors

**Cause**: Clerk JWT template not properly configured or tokens not being passed.

**Solution**:
1. Check Clerk JWT template configuration
2. Verify `window.Clerk.session.getToken({ template: "supabase" })` is working
3. Check browser network tab for Authorization headers

### Issue 2: Users can't access their own data

**Cause**: `requesting_user_id()` function not returning correct user ID.

**Solution**:
1. Test the function directly: `SELECT requesting_user_id();`
2. Verify JWT claims structure matches expectations
3. Check if user_id fields in database match Clerk user IDs

### Issue 3: Admin users can't access admin data

**Cause**: `is_admin()` function not correctly detecting admin role.

**Solution**:
1. Test the function directly: `SELECT is_admin();`
2. Verify `org_role` claim is present in JWT
3. Check if admin role names match expectations ("admin" or "employee")

### Issue 4: Anonymous users can access protected data

**Cause**: RLS policies not properly configured or enabled.

**Solution**:
1. Run `SELECT * FROM check_rls_status();` to verify RLS is enabled
2. Check if policies are correctly applied
3. Verify service role key is not being used in client-side code

## 📊 Performance Testing

### Test Query Performance

Run these queries to check performance impact:

```sql
-- Check query execution time
EXPLAIN ANALYZE SELECT * FROM answers WHERE user_id = requesting_user_id();
EXPLAIN ANALYZE SELECT * FROM scholarships WHERE is_public = true AND is_active = true;

-- Check index usage
SELECT schemaname, tablename, indexname, idx_tup_read, idx_tup_fetch 
FROM pg_stat_user_indexes 
WHERE schemaname = 'public';
```

### Recommended Indexes

Ensure these indexes exist for optimal performance:

```sql
-- User-specific table indexes
CREATE INDEX IF NOT EXISTS idx_answers_user_id ON answers(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_scholarship_applications_user_id ON user_scholarship_applications(user_id);

-- Scholarship filtering indexes
CREATE INDEX IF NOT EXISTS idx_scholarships_public_active ON scholarships(is_public, is_active);
```

## 🎯 Testing Checklist

- [ ] All migrations applied successfully
- [ ] RLS enabled on all tables
- [ ] JWT functions (`requesting_user_id`, `is_admin`, `is_authenticated`) working
- [ ] Anonymous users can only access public data
- [ ] Authenticated users can access their own data + public data
- [ ] Admin users can access all data
- [ ] Insert/Update/Delete operations respect RLS policies
- [ ] Client-side Supabase client uses JWT tokens
- [ ] Server-side Supabase client uses JWT tokens
- [ ] Service role client still works for admin operations
- [ ] Performance is acceptable
- [ ] All existing functionality still works

## 🔍 Debug Tools

### Enable Supabase Logging

To debug RLS issues, enable logging in your Supabase instance:

```sql
-- Enable query logging
ALTER SYSTEM SET log_statement = 'all';
SELECT pg_reload_conf();
```

### Check Current JWT Claims

Add this helper function to debug JWT claims:

```sql
CREATE OR REPLACE FUNCTION debug_jwt_claims()
RETURNS json AS $$
    SELECT current_setting('request.jwt.claims', true)::json;
$$ LANGUAGE SQL;
```

Usage: `SELECT debug_jwt_claims();`

## 📝 Notes

- Always test in a staging environment first
- Keep the service role key secure and only use it in server-side code
- Monitor query performance after RLS implementation
- Consider implementing caching for frequently accessed data
- Document any custom RLS policies for future reference

## 🆘 Emergency Rollback

If RLS causes issues in production:

```sql
-- Temporarily disable RLS on all tables
ALTER TABLE answers DISABLE ROW LEVEL SECURITY;
-- ... repeat for all tables

-- Or use service role to bypass RLS temporarily
-- (Make sure to re-enable and fix issues)
```

Remember: RLS is a security feature - don't disable it permanently without proper consideration! 